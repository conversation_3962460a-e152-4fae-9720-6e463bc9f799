# 牛券优惠券系统工作流学习教程

## 📚 学习目标

通过本教程，您将全面掌握牛券优惠券系统中的工作流相关功能，包括：
- 工作流的核心概念和设计理念
- 状态机模式的实际应用
- 异步任务调度与执行机制
- 消息队列驱动的工作流
- 责任链模式在业务流程中的应用

## 🏗️ 系统架构概览

### 工作流核心模块

```
牛券优惠券系统工作流架构
├── 商家后管模块 (Merchant-Admin)
│   ├── 优惠券创建工作流
│   ├── 推送任务工作流  
│   ├── 定时任务调度
│   └── 责任链验证流程
├── 分发模块 (Distribution)
│   ├── 异步分发工作流
│   ├── 断点续传机制
│   └── 批量处理流程
├── 引擎模块 (Engine)
│   ├── 优惠券状态流转
│   ├── 预约提醒工作流
│   └── 延时消息处理
└── 基础框架 (Framework)
    ├── 幂等性保证
    ├── 异常处理机制
    └── 统一配置管理
```

## 🔄 核心工作流详解

### 1. 优惠券创建工作流

#### 1.1 流程概述
优惠券创建是一个典型的多步骤验证和处理流程，采用责任链模式确保每个环节的独立性和可扩展性。

#### 1.2 关键组件

**责任链上下文管理器**
```java
// 核心类：MerchantAdminChainContext
// 位置：merchant-admin/src/main/java/com/nageoffer/onecoupon/merchant/admin/service/basics/chain/
```

**状态枚举定义**
```java
// 优惠券模板状态
public enum CouponTemplateStatusEnum {
    ACTIVE(0),    // 生效中
    ENDED(1);     // 已结束
}
```

#### 1.3 工作流步骤

1. **防重复提交检查**
   - 使用 `@NoDuplicateSubmit` 注解
   - Redisson 分布式锁机制
   - 防止用户重复点击

2. **责任链验证**
   - 参数非空验证器
   - 格式验证器  
   - 业务规则验证器
   - 权限验证器

3. **分库分表存储**
   - ShardingSphere 分片策略
   - 根据商家ID分库
   - 根据模板ID分表

4. **操作日志记录**
   - BizLog 框架记录
   - 确保行为留痕

### 2. 优惠券推送任务工作流

#### 2.1 定时任务调度机制

**XXL-Job 定时扫描**
```java
@XxlJob(value = "couponTemplateTask")
public void execute() throws Exception {
    // 扫描待执行任务
    // 状态更新为执行中
    // 发送消息队列
}
```

#### 2.2 任务状态流转

```
待执行(PENDING) → 执行中(IN_PROGRESS) → 执行成功(SUCCESS)
                                    ↓
                                执行失败(FAILED)
                                    ↓
                                取消(CANAL)
```

#### 2.3 异步处理机制

1. **消息队列解耦**
   - 后管模块发送任务消息
   - 分发模块异步消费处理
   - RocketMQ 保证消息可靠性

2. **EasyExcel 流式处理**
   - 避免大文件内存溢出
   - 逐行读取处理
   - 支持百万级数据

### 3. 分发模块异步工作流

#### 3.1 断点续传机制

**进度跟踪设计**
```java
// Redis 记录执行进度
String progressKey = "coupon:distribution:progress:" + taskId;
// 记录当前处理行号
stringRedisTemplate.opsForValue().set(progressKey, String.valueOf(rowCount));
```

#### 3.2 批量处理优化

1. **批量大小控制**
   - 每批处理 5000 条记录
   - 减少数据库交互次数
   - 提升整体性能

2. **Lua 脚本原子操作**
   - Redis 层面原子性保证
   - 库存扣减 + 用户记录
   - 减少网络交互

#### 3.3 幂等性保证

1. **唯一索引约束**
   - 数据库层面防重复
   - 用户+优惠券唯一性

2. **异常处理机制**
   - 捕获重复记录异常
   - 单条插入兜底策略

### 4. 引擎模块状态流转工作流

#### 4.1 用户优惠券状态机

```
未使用(UNUSED) → 锁定(LOCKING) → 已使用(USED)
      ↓              ↓
   已过期(EXPIRED)  已撤回(REVOKED)
```

#### 4.2 延时消息处理

**预约提醒工作流**
```java
// RocketMQ 5.x 任意延时消息
// 精准时间推送提醒
@RocketMQMessageListener(
    topic = "coupon_remind_delay",
    consumerGroup = "remind_consumer_group"
)
```

## 🛠️ 技术实现要点

### 1. 幂等性设计

#### 1.1 防重复提交
```java
@NoDuplicateSubmit(
    keyPrefix = "coupon:create:",
    key = "#request.shopNumber + ':' + #request.templateName",
    keyTimeout = 60
)
```

#### 1.2 消息幂等消费
```java
@NoMQDuplicateConsume(
    keyPrefix = "coupon_task_execute:idempotent:",
    key = "#messageWrapper.message.couponTaskId",
    keyTimeout = 120
)
```

### 2. 异步处理策略

#### 2.1 线程池配置
```java
// 异步统计 Excel 行数
ExecutorService executor = Executors.newFixedThreadPool(10);
// Redisson 延时队列兜底
RDelayedQueue<String> delayedQueue = redissonClient.getDelayedQueue(queue);
```

#### 2.2 消息队列配置
```yaml
rocketmq:
  name-server: 127.0.0.1:9876
  producer:
    group: oneCoupon_merchant-admin-service_common-message-execute_pg
    send-message-timeout: 2000
    retry-times-when-send-failed: 1
```

### 3. 性能优化技巧

#### 3.1 批量操作
- 数据库批量插入
- Redis 批量操作
- Lua 脚本减少网络交互

#### 3.2 缓存策略
- 布隆过滤器防缓存穿透
- 分布式锁防缓存击穿
- 缓存空值防缓存雪崩

## 📊 监控与运维

### 1. 日志记录
- 操作日志：BizLog 框架
- 系统日志：Logback 配置
- 消息日志：RocketMQ 消费记录

### 2. 异常处理
- 全局异常处理器
- 业务异常分类
- 失败重试机制

### 3. 性能监控
- 执行时间统计
- 内存使用监控
- 吞吐量分析

## 🎯 学习路径建议

### 第一阶段：基础概念理解
1. 学习责任链模式原理
2. 理解状态机设计思想
3. 掌握异步处理概念

### 第二阶段：核心流程分析
1. 分析优惠券创建流程
2. 研究推送任务调度
3. 理解分发处理机制

### 第三阶段：技术深入
1. 幂等性设计实现
2. 性能优化策略
3. 异常处理机制

### 第四阶段：实践应用
1. 自定义责任链处理器
2. 扩展状态流转逻辑
3. 优化性能瓶颈

## 🔍 核心代码实现分析

### 1. 责任链模式实现

#### 1.1 抽象责任链处理器

```java
/**
 * 抽象商家后管业务责任链组件
 */
public interface MerchantAdminAbstractChainHandler<T> extends Ordered {

    /**
     * 执行责任链逻辑
     * @param requestParam 责任链执行入参
     */
    void handler(T requestParam);

    /**
     * @return 责任链组件标识
     */
    String mark();
}
```

#### 1.2 责任链上下文管理器

```java
@Component
public class MerchantAdminChainContext<T> implements CommandLineRunner, ApplicationContextAware {

    private ApplicationContext applicationContext;

    /**
     * 保存商家后管责任链实现类
     * Key：责任链标识
     * Val：责任链处理器集合
     */
    private final Map<String, List<MerchantAdminAbstractChainHandler>> abstractChainHandlerContainer = new HashMap<>();

    /**
     * 责任链组件执行
     */
    public void handler(String mark, T requestParam) {
        List<MerchantAdminAbstractChainHandler> abstractChainHandlers = abstractChainHandlerContainer.get(mark);
        if (CollectionUtils.isEmpty(abstractChainHandlers)) {
            throw new RuntimeException(String.format("[%s] Chain of Responsibility ID is undefined.", mark));
        }
        abstractChainHandlers.forEach(each -> each.handler(requestParam));
    }

    @Override
    public void run(String... args) throws Exception {
        // 从 Spring IOC 容器中获取所有责任链处理器
        Map<String, MerchantAdminAbstractChainHandler> chainFilterMap =
            applicationContext.getBeansOfType(MerchantAdminAbstractChainHandler.class);

        chainFilterMap.forEach((beanName, bean) -> {
            List<MerchantAdminAbstractChainHandler> abstractChainHandlers =
                abstractChainHandlerContainer.getOrDefault(bean.mark(), new ArrayList<>());
            abstractChainHandlers.add(bean);
            abstractChainHandlerContainer.put(bean.mark(), abstractChainHandlers);
        });

        // 对每个责任链按优先级排序
        abstractChainHandlerContainer.forEach((mark, unsortedChainHandlers) -> {
            unsortedChainHandlers.sort(Comparator.comparing(Ordered::getOrder));
        });
    }
}
```

### 2. 定时任务工作流实现

#### 2.1 XXL-Job 任务处理器

```java
@Component
@RequiredArgsConstructor
public class CouponTaskJobHandler extends IJobHandler {

    private final CouponTaskMapper couponTaskMapper;
    private final CouponTaskActualExecuteProducer couponTaskActualExecuteProducer;

    private static final int MAX_LIMIT = 100;

    @XxlJob(value = "couponTemplateTask")
    public void execute() throws Exception {
        long initId = 0;
        Date now = new Date();

        while (true) {
            // 获取已到执行时间的待执行任务
            List<CouponTaskDO> couponTaskDOList = fetchPendingTasks(initId, now);

            if (CollUtil.isEmpty(couponTaskDOList)) {
                break;
            }

            // 调用分发服务对用户发送优惠券
            for (CouponTaskDO each : couponTaskDOList) {
                distributeCoupon(each);
            }

            // 分页处理，避免一次性加载过多数据
            if (couponTaskDOList.size() < MAX_LIMIT) {
                break;
            }

            // 更新 initId 为当前列表中最大 ID
            initId = couponTaskDOList.stream()
                    .mapToLong(CouponTaskDO::getId)
                    .max()
                    .orElse(initId);
        }
    }

    private void distributeCoupon(CouponTaskDO couponTask) {
        // 修改任务状态为执行中
        CouponTaskDO couponTaskDO = CouponTaskDO.builder()
                .id(couponTask.getId())
                .status(CouponTaskStatusEnum.IN_PROGRESS.getStatus())
                .build();
        couponTaskMapper.updateById(couponTaskDO);

        // 通过消息队列发送消息，由分发服务消费
        CouponTaskExecuteEvent couponTaskExecuteEvent = CouponTaskExecuteEvent.builder()
                .couponTaskId(couponTask.getId())
                .build();
        couponTaskActualExecuteProducer.sendMessage(couponTaskExecuteEvent);
    }

    private List<CouponTaskDO> fetchPendingTasks(long initId, Date now) {
        LambdaQueryWrapper<CouponTaskDO> queryWrapper = Wrappers.lambdaQuery(CouponTaskDO.class)
                .eq(CouponTaskDO::getStatus, CouponTaskStatusEnum.PENDING.getStatus())
                .le(CouponTaskDO::getSendTime, now)
                .gt(CouponTaskDO::getId, initId)
                .last("LIMIT " + MAX_LIMIT);
        return couponTaskMapper.selectList(queryWrapper);
    }
}
```

### 3. 消息队列消费者工作流

#### 3.1 任务执行消费者

```java
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = DistributionRocketMQConstant.TEMPLATE_TASK_EXECUTE_TOPIC_KEY,
        consumerGroup = DistributionRocketMQConstant.TEMPLATE_TASK_EXECUTE_CG_KEY
)
public class CouponTaskExecuteConsumer implements RocketMQListener<MessageWrapper<CouponTaskExecuteEvent>> {

    private final CouponTaskMapper couponTaskMapper;
    private final CouponTemplateMapper couponTemplateMapper;
    private final CouponTaskFailMapper couponTaskFailMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final CouponExecuteDistributionProducer couponExecuteDistributionProducer;

    @NoMQDuplicateConsume(
            keyPrefix = "coupon_task_execute:idempotent:",
            key = "#messageWrapper.message.couponTaskId",
            keyTimeout = 120
    )
    @Override
    public void onMessage(MessageWrapper<CouponTaskExecuteEvent> messageWrapper) {
        log.info("[消费者] 优惠券推送任务正式执行 - 执行消费逻辑，消息体：{}", JSON.toJSONString(messageWrapper));

        var couponTaskId = messageWrapper.getMessage().getCouponTaskId();
        var couponTaskDO = couponTaskMapper.selectById(couponTaskId);

        // 判断任务状态是否为执行中
        if (ObjectUtil.notEqual(couponTaskDO.getStatus(), CouponTaskStatusEnum.IN_PROGRESS.getStatus())) {
            log.warn("[消费者] 优惠券推送任务正式执行 - 推送任务记录状态异常：{}，已终止推送", couponTaskDO.getStatus());
            return;
        }

        // 判断优惠券模板状态是否正确
        var queryWrapper = Wrappers.lambdaQuery(CouponTemplateDO.class)
                .eq(CouponTemplateDO::getId, couponTaskDO.getCouponTemplateId())
                .eq(CouponTemplateDO::getShopNumber, couponTaskDO.getShopNumber());
        var couponTemplateDO = couponTemplateMapper.selectOne(queryWrapper);
        var status = couponTemplateDO.getStatus();

        if (ObjectUtil.notEqual(status, CouponTemplateStatusEnum.ACTIVE.getStatus())) {
            log.error("[消费者] 优惠券推送任务正式执行 - 优惠券ID：{}，优惠券模板状态：{}",
                     couponTaskDO.getCouponTemplateId(), status);
            return;
        }

        // 正式开始执行优惠券推送任务
        ReadExcelDistributionListener readExcelDistributionListener = new ReadExcelDistributionListener(
                couponTaskDO,
                couponTemplateDO,
                couponTaskFailMapper,
                stringRedisTemplate,
                couponExecuteDistributionProducer
        );

        // 使用 EasyExcel 流式读取处理
        EasyExcel.read(couponTaskDO.getFileAddress(), CouponTaskExcelObject.class, readExcelDistributionListener)
                .sheet().doRead();
    }
}
```

## 📝 总结

牛券优惠券系统的工作流设计体现了现代分布式系统的最佳实践：

1. **模块化设计**：每个模块职责清晰，高内聚低耦合
2. **异步处理**：提升系统响应性能和用户体验
3. **可靠性保证**：幂等性、重试机制、异常处理
4. **可扩展性**：责任链模式、策略模式支持业务扩展
5. **可观测性**：完善的日志记录和监控机制

通过深入学习这些工作流实现，您将掌握企业级系统设计的核心技能。
