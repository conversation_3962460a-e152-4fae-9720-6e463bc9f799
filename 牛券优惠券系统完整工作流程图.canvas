{"nodes": [{"id": "start-workflow", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# 工作流启动\n\n**触发方式：**\n- 商家手动创建\n- 定时任务扫描\n- 外部系统调用", "color": "1"}, {"id": "chain-validation", "type": "text", "x": 350, "y": 50, "width": 220, "height": 120, "text": "# 责任链验证\n\n**MerchantAdminChainContext：**\n- 参数非空验证\n- 格式验证\n- 业务规则验证\n- 权限验证\n- 按优先级顺序执行", "color": "2"}, {"id": "duplicate-check", "type": "text", "x": 600, "y": 50, "width": 200, "height": 100, "text": "# 防重复提交\n\n**@NoDuplicateSubmit：**\n- Redisson分布式锁\n- 基于用户+操作生成Key\n- 60秒锁定时间", "color": "3"}, {"id": "state-transition", "type": "text", "x": 100, "y": 200, "width": 220, "height": 120, "text": "# 状态流转管理\n\n**状态枚举：**\n- PENDING → IN_PROGRESS\n- IN_PROGRESS → SUCCESS\n- IN_PROGRESS → FAILED\n- 任意状态 → CANAL", "color": "4"}, {"id": "xxl-job-scheduler", "type": "text", "x": 350, "y": 200, "width": 220, "height": 120, "text": "# XXL-Job定时调度\n\n**CouponTaskJobHandler：**\n- 扫描待执行任务\n- 分页查询优化\n- 状态更新为执行中\n- 发送MQ消息", "color": "5"}, {"id": "mq-async-processing", "type": "text", "x": 600, "y": 200, "width": 220, "height": 120, "text": "# 消息队列异步处理\n\n**RocketMQ：**\n- 系统解耦\n- 异步处理\n- 消息可靠性保证\n- 幂等消费机制", "color": "6"}, {"id": "excel-stream-processing", "type": "text", "x": 100, "y": 350, "width": 220, "height": 120, "text": "# EasyExcel流式处理\n\n**ReadExcelDistributionListener：**\n- 逐行读取避免OOM\n- 支持百万级数据\n- 边读边处理机制\n- 内存使用优化", "color": "1"}, {"id": "progress-tracking", "type": "text", "x": 350, "y": 350, "width": 220, "height": 120, "text": "# 断点续传机制\n\n**Redis进度跟踪：**\n- 记录当前处理行号\n- 应用重启可恢复\n- 跳过已处理数据\n- 失败重试支持", "color": "2"}, {"id": "batch-processing", "type": "text", "x": 600, "y": 350, "width": 220, "height": 120, "text": "# 批量处理优化\n\n**性能提升：**\n- 5000条批量保存\n- 减少数据库交互\n- Lua脚本原子操作\n- 批量Redis操作", "color": "3"}, {"id": "concurrency-control", "type": "text", "x": 100, "y": 500, "width": 220, "height": 120, "text": "# 并发控制机制\n\n**乐观锁防超卖：**\n- 版本号机制\n- 避免库存多扣\n- 自旋重试策略\n- 高并发场景适用", "color": "4"}, {"id": "idempotent-design", "type": "text", "x": 350, "y": 500, "width": 220, "height": 120, "text": "# 幂等性设计\n\n**重复处理防护：**\n- 唯一索引约束\n- 捕获重复异常\n- 单条插入兜底\n- 失败记录追踪", "color": "5"}, {"id": "notification-strategy", "type": "text", "x": 600, "y": 500, "width": 220, "height": 120, "text": "# 通知策略模式\n\n**多渠道支持：**\n- 应用推送\n- 短信通知\n- 邮件发送\n- 站内信息", "color": "6"}, {"id": "delay-message-workflow", "type": "text", "x": 100, "y": 650, "width": 220, "height": 120, "text": "# 延时消息工作流\n\n**RocketMQ 5.x：**\n- 任意延时消息\n- 精准时间推送\n- 预约提醒功能\n- 线程池消费", "color": "1"}, {"id": "state-machine-engine", "type": "text", "x": 350, "y": 650, "width": 220, "height": 120, "text": "# 状态机引擎\n\n**用户优惠券状态：**\n- UNUSED → LOCKING\n- LOCKING → USED\n- UNUSED → EXPIRED\n- LOCKING → REVOKED", "color": "2"}, {"id": "monitoring-logging", "type": "text", "x": 600, "y": 650, "width": 220, "height": 120, "text": "# 监控与日志\n\n**可观测性：**\n- BizLog操作日志\n- 系统性能监控\n- 异常追踪记录\n- 业务指标统计", "color": "3"}, {"id": "workflow-complete", "type": "text", "x": 350, "y": 800, "width": 220, "height": 100, "text": "# 工作流完成\n\n**结果输出：**\n- 成功/失败状态\n- 处理结果统计\n- 异常记录导出\n- 性能指标报告", "color": "4"}], "edges": [{"id": "edge-1", "fromNode": "start-workflow", "toNode": "chain-validation", "color": "1", "label": "1. 触发验证"}, {"id": "edge-2", "fromNode": "chain-validation", "toNode": "duplicate-check", "color": "2", "label": "2. 防重检查"}, {"id": "edge-3", "fromNode": "duplicate-check", "toNode": "state-transition", "color": "3", "label": "3. 状态流转"}, {"id": "edge-4", "fromNode": "state-transition", "toNode": "xxl-job-scheduler", "color": "4", "label": "4. 定时调度"}, {"id": "edge-5", "fromNode": "xxl-job-scheduler", "toNode": "mq-async-processing", "color": "5", "label": "5. 异步处理"}, {"id": "edge-6", "fromNode": "mq-async-processing", "toNode": "excel-stream-processing", "color": "6", "label": "6. 流式处理"}, {"id": "edge-7", "fromNode": "excel-stream-processing", "toNode": "progress-tracking", "color": "1", "label": "7. 进度跟踪"}, {"id": "edge-8", "fromNode": "progress-tracking", "toNode": "batch-processing", "color": "2", "label": "8. 批量处理"}, {"id": "edge-9", "fromNode": "batch-processing", "toNode": "concurrency-control", "color": "3", "label": "9. 并发控制"}, {"id": "edge-10", "fromNode": "concurrency-control", "toNode": "idempotent-design", "color": "4", "label": "10. 幂等保证"}, {"id": "edge-11", "fromNode": "idempotent-design", "toNode": "notification-strategy", "color": "5", "label": "11. 通知发送"}, {"id": "edge-12", "fromNode": "notification-strategy", "toNode": "delay-message-workflow", "color": "6", "label": "12. 延时消息"}, {"id": "edge-13", "fromNode": "delay-message-workflow", "toNode": "state-machine-engine", "color": "1", "label": "13. 状态机"}, {"id": "edge-14", "fromNode": "state-machine-engine", "toNode": "monitoring-logging", "color": "2", "label": "14. 监控日志"}, {"id": "edge-15", "fromNode": "monitoring-logging", "toNode": "workflow-complete", "color": "3", "label": "15. 完成"}]}