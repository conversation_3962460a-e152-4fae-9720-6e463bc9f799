<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.nageoffer.onecoupon</groupId>
    <artifactId>onecoupon-all</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <description>
        🔥热门推荐🔥牛券系统，春招、秋招、应届、社招项目。SpringBoot3+Java17+SpringCloudAlibaba+RocketMQ+ElasticSearch等技术架构，完成优惠券秒杀+分发+结算+搜索等服务，帮助学生主打就业的项目。
    </description>

    <developers>
        <developer>
            <name>马丁</name>
            <email><EMAIL></email>
            <url>https://github.com/magestacks</url>
            <organization>Apache and openGoofy</organization>
        </developer>

        <developer>
            <name>优雅</name>
            <email><EMAIL></email>
        </developer>

        <developer>
            <name>Henry</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <modules>
        <!-- 基础架构模块：仅包含公共内容，一个包引入构建所有组件 -->
        <!-- 可能会有项目用不到这么多功能。如果想精细化引入，参考 12306 frameworks 模块 -->
        <module>framework</module>
        <!-- 分发模块：负责按批次分发用户优惠券，可提供应用弹框推送、站内信或短信通知等 -->
        <module>distribution</module>
        <!-- 结算模块：负责用户下单时订单金额计算功能，因和订单相关联，该服务流量较大 -->
        <module>settlement</module>
        <!-- 搜索模块：提供用户优惠券搜索功能 -->
        <module>search</module>
        <!-- 引擎模块：负责优惠券单个查看、列表查看、锁定以及核销等功能 -->
        <module>engine</module>
        <!-- 为何其他模块仅一个单词，而后管是两个？ -->
        <!-- 这是因为在企业的实际运营中，并非仅有单一的优惠券管理后台，而是普遍存在统称为“商家后台”的多功能管理平台 -->
        <module>merchant-admin</module> <!-- 后管模块：创建优惠券、店家查看以及管理优惠券、创建优惠券发放批次等 -->
        <!-- 网关模块：提供动态路由、日志记录以及限流熔断等功能 -->
        <module>gateway</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <spring-boot.version>3.0.7</spring-boot.version>
        <spring-cloud.version>2022.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2022.0.0.0-RC2</spring-cloud-alibaba.version>
        <mybatis-spring-boot-starter.version>3.0.2</mybatis-spring-boot-starter.version>
        <shardingsphere.version>5.3.2</shardingsphere.version>
        <fastjson2.version>2.0.36</fastjson2.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <dozer-core.version>6.5.2</dozer-core.version>
        <hutool-all.version>5.8.27</hutool-all.version>
        <redisson.version>3.27.2</redisson.version>
        <guava.version>30.0-jre</guava.version>
        <jsoup.version>1.15.3</jsoup.version>
        <knife4j-openapi3-jakarta-spring-boot-starter.version>4.5.0
        </knife4j-openapi3-jakarta-spring-boot-starter.version>
        <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
        <javafaker.version>1.0.2</javafaker.version>
        <easyexcel.version>4.0.1</easyexcel.version>
        <xxl-job.version>2.4.1</xxl-job.version>
        <rocketmq-spring-boot-starter.version>2.3.0</rocketmq-spring-boot-starter.version>
        <spotless-maven-plugin.version>2.22.1</spotless-maven-plugin.version>
        <maven-compiler-plugin.version>3.6.1</maven-compiler-plugin.version>
        <elasticsearch-spring-boot-starter.version>2.6.12</elasticsearch-spring-boot-starter.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <!-- 如果你拉下来某一个分支的代码运行，结果标签下依赖爆红，请忽略改问题 -->
    <!-- 因为 dependencyManagement 只是负责管理标签并不会真正下载依赖，只有在子 Module 中引入具体依赖才会真正去下载 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.dozermapper</groupId>
                <artifactId>dozer-core</artifactId>
                <version>${dozer-core.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j-openapi3-jakarta-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.mouzt</groupId>
                <artifactId>bizlog-sdk</artifactId>
                <version>${bizlog-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.javafaker</groupId>
                <artifactId>javafaker</artifactId>
                <version>${javafaker.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>${elasticsearch-spring-boot-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>${spotless-maven-plugin.version}</version>
                <configuration>
                    <java>
                        <!--<eclipse>
                            <file>${maven.multiModuleProjectDirectory}/format/one-coupon_spotless_formatter.xml</file>
                        </eclipse>-->
                        <licenseHeader>
                            <!-- ${maven.multiModuleProjectDirectory} 爆红属于正常，并不影响编译或者运行，忽略就好 -->
                            <file>${maven.multiModuleProjectDirectory}/copyright/copyright.txt</file>
                        </licenseHeader>
                    </java>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>apply</goal>
                        </goals>
                        <phase>compile</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
