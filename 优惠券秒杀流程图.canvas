{"nodes": [{"id": "user-request-node", "type": "text", "x": 100, "y": 50, "width": 150, "height": 80, "text": "# 用户请求\n\n用户点击抢购\n优惠券按钮", "color": "1"}, {"id": "gateway-route-node", "type": "text", "x": 300, "y": 50, "width": 150, "height": 80, "text": "# 网关路由\n\n请求经过Gateway\n路由到Engine模块", "color": "2"}, {"id": "lua-precheck-node", "type": "text", "x": 500, "y": 50, "width": 200, "height": 100, "text": "# Lua脚本预检查\n\n**Redis层面快速失败：**\n- 检查优惠券库存\n- 检查用户领取资格\n- 库存不足直接返回\n- 避免无效数据库访问", "color": "3"}, {"id": "precheck-fail-node", "type": "text", "x": 750, "y": 50, "width": 150, "height": 80, "text": "# 预检查失败\n\n快速返回\n\"已抢完\"", "color": "6"}, {"id": "transaction-start-node", "type": "text", "x": 100, "y": 200, "width": 180, "height": 100, "text": "# 开启编程式事务\n\n**减少事务时间：**\n- 手动控制事务边界\n- 只在必要时开启\n- 快速提交或回滚", "color": "4"}, {"id": "optimistic-lock-node", "type": "text", "x": 320, "y": 200, "width": 200, "height": 120, "text": "# 乐观锁扣减库存\n\n**SQL示例：**\n```sql\nUPDATE coupon_template \nSET stock = stock - 1,\n    version = version + 1\nWHERE id = ? \n  AND stock > 0 \n  AND version = ?\n```", "color": "5"}, {"id": "stock-deduct-success-node", "type": "text", "x": 560, "y": 200, "width": 150, "height": 80, "text": "# 扣减成功\n\n库存扣减\n版本号更新", "color": "1"}, {"id": "stock-deduct-fail-node", "type": "text", "x": 560, "y": 320, "width": 150, "height": 80, "text": "# 扣减失败\n\n库存不足或\n版本冲突", "color": "6"}, {"id": "user-coupon-create-node", "type": "text", "x": 100, "y": 350, "width": 200, "height": 120, "text": "# 创建用户优惠券\n\n**唯一索引保证幂等：**\n- 用户ID + 优惠券ID\n- 防止重复领取\n- 捕获重复异常\n- 自旋重试机制", "color": "2"}, {"id": "transaction-commit-node", "type": "text", "x": 350, "y": 350, "width": 150, "height": 80, "text": "# 事务提交\n\n数据库操作\n全部成功", "color": "3"}, {"id": "binlog-listen-node", "type": "text", "x": 550, "y": 350, "width": 200, "height": 120, "text": "# Binlog监听\n\n**异步缓存更新：**\n- Canal监听数据变更\n- 异步更新Redis缓存\n- 保证最终一致性\n- 应对缓存更新失败", "color": "4"}, {"id": "write-after-read-node", "type": "text", "x": 100, "y": 500, "width": 200, "height": 100, "text": "# 写后查询策略\n\n**应对极端情况：**\n- Redis持久化失败\n- 主从复制延迟\n- 查询数据库兜底", "color": "5"}, {"id": "success-response-node", "type": "text", "x": 350, "y": 500, "width": 150, "height": 80, "text": "# 抢购成功\n\n返回用户\n优惠券信息", "color": "1"}, {"id": "fail-response-node", "type": "text", "x": 550, "y": 500, "width": 150, "height": 80, "text": "# 抢购失败\n\n返回失败原因\n引导用户重试", "color": "6"}], "edges": [{"id": "edge-1", "fromNode": "user-request-node", "toNode": "gateway-route-node", "color": "1", "label": "1. 发起请求"}, {"id": "edge-2", "fromNode": "gateway-route-node", "toNode": "lua-precheck-node", "color": "2", "label": "2. 路由转发"}, {"id": "edge-3", "fromNode": "lua-precheck-node", "toNode": "precheck-fail-node", "color": "3", "label": "3a. 预检查失败"}, {"id": "edge-4", "fromNode": "lua-precheck-node", "toNode": "transaction-start-node", "color": "4", "label": "3b. 预检查通过"}, {"id": "edge-5", "fromNode": "transaction-start-node", "toNode": "optimistic-lock-node", "color": "5", "label": "4. 开启事务"}, {"id": "edge-6", "fromNode": "optimistic-lock-node", "toNode": "stock-deduct-success-node", "color": "6", "label": "5a. 扣减成功"}, {"id": "edge-7", "fromNode": "optimistic-lock-node", "toNode": "stock-deduct-fail-node", "color": "1", "label": "5b. 扣减失败"}, {"id": "edge-8", "fromNode": "stock-deduct-success-node", "toNode": "user-coupon-create-node", "color": "2", "label": "6. 创建用户券"}, {"id": "edge-9", "fromNode": "user-coupon-create-node", "toNode": "transaction-commit-node", "color": "3", "label": "7. 提交事务"}, {"id": "edge-10", "fromNode": "transaction-commit-node", "toNode": "binlog-listen-node", "color": "4", "label": "8. 触发Binlog"}, {"id": "edge-11", "fromNode": "binlog-listen-node", "toNode": "write-after-read-node", "color": "5", "label": "9. 缓存更新"}, {"id": "edge-12", "fromNode": "write-after-read-node", "toNode": "success-response-node", "color": "6", "label": "10. 返回成功"}, {"id": "edge-13", "fromNode": "stock-deduct-fail-node", "toNode": "fail-response-node", "color": "1", "label": "返回失败"}, {"id": "edge-14", "fromNode": "precheck-fail-node", "toNode": "fail-response-node", "color": "2", "label": "快速失败"}]}