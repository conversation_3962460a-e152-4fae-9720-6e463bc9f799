# 牛券优惠券系统工作流核心代码解析

## 🎯 核心工作流代码深度解析

### 1. ReadExcelDistributionListener - 流式处理核心

这是整个分发系统的核心组件，展现了企业级大数据处理的最佳实践。

<augment_code_snippet path="distribution/src/main/java/com/nageoffer/onecoupon/distribution/service/handler/excel/ReadExcelDistributionListener.java" mode="EXCERPT">
```java
@RequiredArgsConstructor
public class ReadExcelDistributionListener extends AnalysisEventListener<CouponTaskExcelObject> {
    
    private final CouponTaskDO couponTaskDO;
    private final CouponTemplateDO couponTemplateDO;
    private final CouponTaskFailMapper couponTaskFailMapper;
    private final StringRedisTemplate stringRedisTemplate;
    private final CouponExecuteDistributionProducer couponExecuteDistributionProducer;
    
    private int rowCount = 1;
    private final static String STOCK_DECREMENT_AND_BATCH_SAVE_USER_RECORD_LUA_PATH = "lua/stock_decrement_and_batch_save_user_record.lua";
    private final static int BATCH_USER_COUPON_SIZE = 5000;
```
</augment_code_snippet>

#### 1.1 断点续传机制实现

<augment_code_snippet path="distribution/src/main/java/com/nageoffer/onecoupon/distribution/service/handler/excel/ReadExcelDistributionListener.java" mode="EXCERPT">
```java
@Override
public void invoke(CouponTaskExcelObject data, AnalysisContext context) {
    Long couponTaskId = couponTaskDO.getId();
    
    // 获取当前进度，判断是否已经执行过。如果已执行，则跳过即可，防止执行到一半应用宕机
    String templateTaskExecuteProgressKey = String.format(DistributionRedisConstant.TEMPLATE_TASK_EXECUTE_PROGRESS_KEY, couponTaskId);
    String progress = stringRedisTemplate.opsForValue().get(templateTaskExecuteProgressKey);
    if (StrUtil.isNotBlank(progress) && Integer.parseInt(progress) >= rowCount) {
        ++rowCount;
        return;
    }
```
</augment_code_snippet>

**设计亮点分析**：
- **Redis进度跟踪**：使用Redis记录当前处理的行号，确保应用重启后能从断点继续
- **幂等性保证**：通过行号比较避免重复处理
- **内存优化**：逐行处理而非全量加载

#### 1.2 Lua脚本原子操作

<augment_code_snippet path="distribution/src/main/java/com/nageoffer/onecoupon/distribution/service/handler/excel/ReadExcelDistributionListener.java" mode="EXCERPT">
```java
// 获取 LUA 脚本，并保存到 Hutool 的单例管理容器，下次直接获取不需要加载
DefaultRedisScript<Long> buildLuaScript = Singleton.get(STOCK_DECREMENT_AND_BATCH_SAVE_USER_RECORD_LUA_PATH, () -> {
    DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
    redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource(STOCK_DECREMENT_AND_BATCH_SAVE_USER_RECORD_LUA_PATH)));
    redisScript.setResultType(Long.class);
    return redisScript;
});

// 执行 LUA 脚本进行扣减库存以及增加 Redis 用户领券记录
String couponTemplateKey = String.format(EngineRedisConstant.COUPON_TEMPLATE_KEY, couponTemplateDO.getId());
String batchUserSetKey = String.format(DistributionRedisConstant.TEMPLATE_TASK_EXECUTE_BATCH_USER_KEY, couponTaskId);
Map<Object, Object> userRowNumMap = MapUtil.builder()
        .put("userId", data.getUserId())
        .put("rowNum", rowCount + 1)
        .build();
Long combinedFiled = stringRedisTemplate.execute(buildLuaScript, ListUtil.of(couponTemplateKey, batchUserSetKey), JSON.toJSONString(userRowNumMap));
```
</augment_code_snippet>

**技术要点**：
- **单例模式优化**：Lua脚本只加载一次，提升性能
- **原子性保证**：库存扣减和用户记录添加在一个Lua脚本中完成
- **位运算优化**：返回值使用位运算编码多个信息

#### 1.3 批量处理与消息发送

<augment_code_snippet path="distribution/src/main/java/com/nageoffer/onecoupon/distribution/service/handler/excel/ReadExcelDistributionListener.java" mode="EXCERPT">
```java
// firstField 为 false 说明优惠券已经没有库存了
boolean firstField = StockDecrementReturnCombinedUtil.extractFirstField(combinedFiled);
if (!firstField) {
    // 同步当前执行进度到缓存
    stringRedisTemplate.opsForValue().set(templateTaskExecuteProgressKey, String.valueOf(rowCount));
    ++rowCount;
    
    // 添加到 t_coupon_task_fail 并标记错误原因，方便后续查看未成功发送的原因和记录
    Map<Object, Object> objectMap = MapUtil.builder()
            .put("rowNum", rowCount + 1)
            .put("cause", "优惠券模板无库存")
            .build();
    CouponTaskFailDO couponTaskFailDO = CouponTaskFailDO.builder()
            .batchId(couponTaskDO.getBatchId())
            .jsonObject(JSON.toJSONString(objectMap, SerializerFeature.WriteMapNullValue))
            .build();
    couponTaskFailMapper.insert(couponTaskFailDO);
    return;
}

// 获取用户领券集合长度
int batchUserSetSize = StockDecrementReturnCombinedUtil.extractSecondField(combinedFiled.intValue());

// 如果没有消息通知需求，仅在 batchUserSetSize = BATCH_USER_COUPON_SIZE 时发送消息消费
if (batchUserSetSize < BATCH_USER_COUPON_SIZE && StrUtil.isBlank(couponTaskDO.getNotifyType())) {
    // 同步当前 Excel 执行进度到缓存
    stringRedisTemplate.opsForValue().set(templateTaskExecuteProgressKey, String.valueOf(rowCount));
    ++rowCount;
    return;
}
```
</augment_code_snippet>

**核心设计理念**：
- **失败记录追踪**：详细记录每个失败的原因和行号
- **批量优化**：达到5000条才触发消息发送，减少网络开销
- **条件触发**：根据通知类型决定是否立即发送消息

### 2. XXL-Job定时任务工作流

<augment_code_snippet path="merchant-admin/src/main/java/com/nageoffer/onecoupon/merchant/admin/job/CouponTaskJobHandler.java" mode="EXCERPT">
```java
@XxlJob(value = "couponTemplateTask")
public void execute() throws Exception {
    long initId = 0;
    Date now = new Date();
    
    while (true) {
        // 获取已到执行时间待执行的优惠券定时分发任务
        List<CouponTaskDO> couponTaskDOList = fetchPendingTasks(initId, now);
        
        if (CollUtil.isEmpty(couponTaskDOList)) {
            break;
        }
        
        // 调用分发服务对用户发送优惠券
        for (CouponTaskDO each : couponTaskDOList) {
            distributeCoupon(each);
        }
        
        // 查询出来的数据如果小于 MAX_LIMIT 意味着后面将不再有数据，返回即可
        if (couponTaskDOList.size() < MAX_LIMIT) {
            break;
        }
        
        // 更新 initId 为当前列表中最大 ID
        initId = couponTaskDOList.stream()
                .mapToLong(CouponTaskDO::getId)
                .max()
                .orElse(initId);
    }
}
```
</augment_code_snippet>

**分页查询优化**：
- **游标分页**：使用ID作为游标，避免深分页性能问题
- **批量处理**：每次处理100条记录，平衡内存和性能
- **状态管理**：先更新状态为执行中，再发送消息

### 3. 责任链模式实现

<augment_code_snippet path="merchant-admin/src/main/java/com/nageoffer/onecoupon/merchant/admin/service/basics/chain/MerchantAdminChainContext.java" mode="EXCERPT">
```java
@Override
public void run(String... args) throws Exception {
    // 从 Spring IOC 容器中获取指定接口 Spring Bean 集合
    Map<String, MerchantAdminAbstractChainHandler> chainFilterMap = applicationContext.getBeansOfType(MerchantAdminAbstractChainHandler.class);
    chainFilterMap.forEach((beanName, bean) -> {
        // 判断 Mark 是否已经存在抽象责任链容器中，如果已经存在直接向集合新增；如果不存在，创建 Mark 和对应的集合
        List<MerchantAdminAbstractChainHandler> abstractChainHandlers = abstractChainHandlerContainer.getOrDefault(bean.mark(), new ArrayList<>());
        abstractChainHandlers.add(bean);
        abstractChainHandlerContainer.put(bean.mark(), abstractChainHandlers);
    });
    abstractChainHandlerContainer.forEach((mark, unsortedChainHandlers) -> {
        // 对每个 Mark 对应的责任链实现类集合进行排序，优先级小的在前
        unsortedChainHandlers.sort(Comparator.comparing(Ordered::getOrder));
    });
}
```
</augment_code_snippet>

**自动装配机制**：
- **Spring集成**：自动发现所有责任链处理器
- **标识分组**：通过mark()方法进行分组管理
- **优先级排序**：支持Ordered接口进行排序

## 🔍 工作流设计模式总结

### 1. 观察者模式 - EasyExcel监听器
- **事件驱动**：每读取一行数据触发一次处理
- **解耦设计**：读取逻辑与处理逻辑分离
- **内存友好**：流式处理避免大文件内存溢出

### 2. 模板方法模式 - 抽象责任链
- **算法骨架**：定义处理流程框架
- **扩展点**：子类实现具体验证逻辑
- **复用性**：通用的责任链管理机制

### 3. 策略模式 - 通知方式
- **算法族**：多种通知方式（短信、邮件、推送）
- **运行时切换**：根据配置动态选择通知策略
- **易扩展**：新增通知方式无需修改现有代码

### 4. 状态模式 - 任务状态管理
- **状态封装**：每个状态有明确的行为定义
- **状态转换**：严格控制状态转换规则
- **行为变化**：不同状态下的处理逻辑不同

## 🚀 性能优化技巧

### 1. 批量处理优化
```java
private final static int BATCH_USER_COUPON_SIZE = 5000;
```
- **减少网络IO**：批量发送消息减少网络开销
- **提升吞吐量**：批量数据库操作提升性能
- **内存控制**：控制批量大小避免内存溢出

### 2. Lua脚本原子操作
- **原子性保证**：多个Redis操作在一个脚本中完成
- **网络优化**：减少客户端与Redis的交互次数
- **并发安全**：避免并发情况下的数据不一致

### 3. 单例模式缓存
```java
DefaultRedisScript<Long> buildLuaScript = Singleton.get(STOCK_DECREMENT_AND_BATCH_SAVE_USER_RECORD_LUA_PATH, () -> {
    // 脚本加载逻辑
});
```
- **资源复用**：Lua脚本只加载一次
- **内存优化**：避免重复创建对象
- **性能提升**：减少脚本编译时间

## 📊 监控与可观测性

### 1. 进度跟踪
- **实时进度**：Redis记录当前处理进度
- **断点续传**：支持从中断点恢复
- **状态监控**：任务状态实时更新

### 2. 异常处理
- **失败记录**：详细记录失败原因和位置
- **重试机制**：支持失败重试
- **告警通知**：异常情况及时告警

### 3. 性能指标
- **处理速度**：记录每秒处理行数
- **成功率**：统计处理成功率
- **资源使用**：监控内存和CPU使用情况

通过这些核心代码的深入分析，您可以看到牛券优惠券系统在工作流设计上的精妙之处，这些都是企业级系统开发的宝贵经验！
