{"nodes": [{"id": "task-start", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# 任务启动\n\n**应用启动/重启：**\n- 检查是否有未完成任务\n- 获取上次执行点位\n- 从断点位置继续", "color": "1"}, {"id": "checkpoint-recovery", "type": "text", "x": 350, "y": 50, "width": 220, "height": 120, "text": "# 执行点位恢复\n\n**Redis进度查询：**\n- Key: progress:{taskId}\n- 获取已处理行号\n- rowCount = 上次中断位置\n- 实现零数据丢失", "color": "2"}, {"id": "excel-stream", "type": "text", "x": 600, "y": 50, "width": 220, "height": 120, "text": "# EasyExcel流式读取\n\n**逐行处理机制：**\n- 从断点行号开始\n- 内存占用极小\n- 支持百万级数据\n- 边读边处理", "color": "3"}, {"id": "row-check", "type": "text", "x": 100, "y": 200, "width": 200, "height": 100, "text": "# 行号检查\n\n**幂等性保证：**\n- 比较当前行号\n- 跳过已处理行\n- 避免重复处理", "color": "4"}, {"id": "lua-atomic", "type": "text", "x": 350, "y": 200, "width": 220, "height": 120, "text": "# Lua脚本原子操作\n\n**一次性完成：**\n- 库存检查与扣减\n- 用户数据添加到Set\n- 返回批量计数\n- 保证原子性", "color": "5"}, {"id": "batch-accumulate", "type": "text", "x": 600, "y": 200, "width": 220, "height": 120, "text": "# 批量数据累积\n\n**Redis Set管理：**\n- 用户数据存储到Set\n- 实时统计数量\n- 达到5000条触发\n- 内存高效管理", "color": "6"}, {"id": "progress-update", "type": "text", "x": 100, "y": 350, "width": 200, "height": 100, "text": "# 实时进度更新\n\n**每行处理后：**\n- 立即更新Redis\n- 记录当前行号\n- 支持断点续传", "color": "1"}, {"id": "batch-trigger", "type": "text", "x": 350, "y": 350, "width": 220, "height": 120, "text": "# 批量处理触发\n\n**条件判断：**\n- 达到5000条数据\n- 或Excel解析完成\n- 发送RocketMQ消息\n- 异步批量处理", "color": "2"}, {"id": "async-batch", "type": "text", "x": 600, "y": 350, "width": 220, "height": 120, "text": "# 异步批量消费\n\n**CouponExecuteDistributionConsumer：**\n- 消费批量处理消息\n- 从Redis获取数据\n- 批量数据库操作\n- 性能提升50倍", "color": "3"}, {"id": "database-batch", "type": "text", "x": 100, "y": 500, "width": 200, "height": 100, "text": "# 数据库批量操作\n\n**insertBatch：**\n- 5000条批量插入\n- 失败单条兜底\n- 乐观锁防超卖", "color": "4"}, {"id": "cache-batch-update", "type": "text", "x": 350, "y": 500, "width": 220, "height": 120, "text": "# 批量缓存更新\n\n**batch_user_coupon_list.lua：**\n- 批量更新用户优惠券缓存\n- ZSet时间排序存储\n- 提升查询性能\n- 保证数据一致性", "color": "5"}, {"id": "failure-handling", "type": "text", "x": 600, "y": 500, "width": 220, "height": 120, "text": "# 失败处理机制\n\n**多级容错：**\n- 批量失败→单条重试\n- 详细失败记录\n- Excel失败报告\n- 便于问题排查", "color": "6"}, {"id": "performance-stats", "type": "text", "x": 100, "y": 650, "width": 200, "height": 100, "text": "# 性能统计\n\n**关键指标：**\n- 处理速度提升50倍\n- 内存使用降低99%\n- 零数据丢失\n- 断电秒级恢复", "color": "1"}, {"id": "bit-optimization", "type": "text", "x": 350, "y": 650, "width": 220, "height": 120, "text": "# 位运算优化\n\n**数据传输优化：**\n- 13位存储批量计数\n- 1位存储成功标识\n- 减少网络传输\n- 提升解析性能", "color": "2"}, {"id": "final-completion", "type": "text", "x": 600, "y": 650, "width": 220, "height": 120, "text": "# 任务完成\n\n**最终状态：**\n- 更新任务状态为成功\n- 生成处理报告\n- 清理临时数据\n- 释放系统资源", "color": "3"}], "edges": [{"id": "edge-1", "fromNode": "task-start", "toNode": "checkpoint-recovery", "color": "1", "label": "1. 断点恢复"}, {"id": "edge-2", "fromNode": "checkpoint-recovery", "toNode": "excel-stream", "color": "2", "label": "2. 流式读取"}, {"id": "edge-3", "fromNode": "excel-stream", "toNode": "row-check", "color": "3", "label": "3. 逐行处理"}, {"id": "edge-4", "fromNode": "row-check", "toNode": "lua-atomic", "color": "4", "label": "4. 未处理继续"}, {"id": "edge-5", "fromNode": "lua-atomic", "toNode": "batch-accumulate", "color": "5", "label": "5. 数据累积"}, {"id": "edge-6", "fromNode": "batch-accumulate", "toNode": "progress-update", "color": "6", "label": "6. 进度更新"}, {"id": "edge-7", "fromNode": "progress-update", "toNode": "batch-trigger", "color": "1", "label": "7. 批量判断"}, {"id": "edge-8", "fromNode": "batch-trigger", "toNode": "async-batch", "color": "2", "label": "8. 异步处理"}, {"id": "edge-9", "fromNode": "async-batch", "toNode": "database-batch", "color": "3", "label": "9. 数据库操作"}, {"id": "edge-10", "fromNode": "database-batch", "toNode": "cache-batch-update", "color": "4", "label": "10. 缓存更新"}, {"id": "edge-11", "fromNode": "cache-batch-update", "toNode": "failure-handling", "color": "5", "label": "11. 异常处理"}, {"id": "edge-12", "fromNode": "failure-handling", "toNode": "performance-stats", "color": "6", "label": "12. 性能统计"}, {"id": "edge-13", "fromNode": "performance-stats", "toNode": "bit-optimization", "color": "1", "label": "13. 优化统计"}, {"id": "edge-14", "fromNode": "bit-optimization", "toNode": "final-completion", "color": "2", "label": "14. 任务完成"}, {"id": "edge-skip", "fromNode": "row-check", "toNode": "progress-update", "color": "3", "label": "已处理跳过"}, {"id": "edge-continue", "fromNode": "batch-trigger", "toNode": "excel-stream", "color": "4", "label": "继续读取"}, {"id": "edge-recovery", "fromNode": "final-completion", "toNode": "task-start", "color": "5", "label": "断电恢复"}]}