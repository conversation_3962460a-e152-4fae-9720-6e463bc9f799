{"nodes": [{"id": "excel-start", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# Excel文件开始处理\n\n**EasyExcel流式读取：**\n- 逐行读取避免OOM\n- 监听器模式处理\n- 支持百万级数据", "color": "1"}, {"id": "progress-check", "type": "text", "x": 350, "y": 50, "width": 220, "height": 120, "text": "# 断点续传检查\n\n**Redis进度跟踪：**\n- Key: progress:{taskId}\n- 获取已处理行号\n- 比较当前行号\n- 跳过已处理数据", "color": "2"}, {"id": "lua-atomic", "type": "text", "x": 600, "y": 50, "width": 220, "height": 120, "text": "# Lua脚本原子操作\n\n**stock_decrement_and_batch_save：**\n- 检查库存充足性\n- 原子扣减库存\n- 添加用户到Set集合\n- 返回组合结果", "color": "3"}, {"id": "stock-check", "type": "text", "x": 100, "y": 200, "width": 200, "height": 100, "text": "# 库存检查结果\n\n**判断扣减结果：**\n- 成功：继续处理\n- 失败：记录失败原因\n- 更新进度", "color": "4"}, {"id": "batch-size-check", "type": "text", "x": 350, "y": 200, "width": 220, "height": 120, "text": "# 批量大小检查\n\n**BATCH_USER_COUPON_SIZE = 5000：**\n- 提取Set集合长度\n- 判断是否达到批量大小\n- 检查通知类型需求", "color": "5"}, {"id": "message-trigger", "type": "text", "x": 600, "y": 200, "width": 220, "height": 120, "text": "# 消息队列触发\n\n**RocketMQ异步处理：**\n- 达到5000条触发\n- 发送分发消息\n- 异步批量处理\n- 系统解耦", "color": "6"}, {"id": "progress-update", "type": "text", "x": 100, "y": 350, "width": 200, "height": 100, "text": "# 进度更新\n\n**Redis实时更新：**\n- 记录当前行号\n- 原子性操作\n- 支持断点恢复", "color": "1"}, {"id": "batch-consumer", "type": "text", "x": 350, "y": 350, "width": 220, "height": 120, "text": "# 批量消费处理\n\n**CouponExecuteDistributionConsumer：**\n- 消费分发消息\n- 从Redis获取用户集合\n- 批量数据库操作\n- 异常处理机制", "color": "2"}, {"id": "database-batch", "type": "text", "x": 600, "y": 350, "width": 220, "height": 120, "text": "# 数据库批量操作\n\n**性能优化策略：**\n- 批量插入5000条\n- 乐观锁防超卖\n- 失败单条兜底\n- 异常记录追踪", "color": "3"}, {"id": "fail-handling", "type": "text", "x": 100, "y": 500, "width": 200, "height": 100, "text": "# 失败处理机制\n\n**详细记录：**\n- 失败行号\n- 失败原因\n- Excel导出\n- 便于排查", "color": "4"}, {"id": "cache-update", "type": "text", "x": 350, "y": 500, "width": 220, "height": 120, "text": "# 缓存更新\n\n**batch_user_coupon_list.lua：**\n- 批量更新用户优惠券缓存\n- ZSet时间排序\n- 提升查询性能\n- 数据一致性", "color": "5"}, {"id": "completion-check", "type": "text", "x": 600, "y": 500, "width": 220, "height": 120, "text": "# 完成状态检查\n\n**distributionEndFlag：**\n- Excel解析完成标识\n- 处理剩余数据\n- 生成失败报告\n- 任务状态更新", "color": "6"}, {"id": "performance-monitor", "type": "text", "x": 100, "y": 650, "width": 200, "height": 100, "text": "# 性能监控\n\n**关键指标：**\n- 处理速度\n- 成功率\n- 内存使用\n- 错误统计", "color": "1"}, {"id": "bit-optimization", "type": "text", "x": 350, "y": 650, "width": 220, "height": 120, "text": "# 位运算优化\n\n**StockDecrementReturnCombinedUtil：**\n- 13位表示用户数量\n- 1位表示成功标识\n- 减少网络传输\n- 提升解析性能", "color": "2"}, {"id": "final-result", "type": "text", "x": 600, "y": 650, "width": 220, "height": 120, "text": "# 最终结果\n\n**处理完成：**\n- 成功分发统计\n- 失败记录导出\n- 性能指标报告\n- 任务状态更新", "color": "3"}], "edges": [{"id": "edge-1", "fromNode": "excel-start", "toNode": "progress-check", "color": "1", "label": "1. 逐行读取"}, {"id": "edge-2", "fromNode": "progress-check", "toNode": "lua-atomic", "color": "2", "label": "2. 未处理继续"}, {"id": "edge-3", "fromNode": "lua-atomic", "toNode": "stock-check", "color": "3", "label": "3. 执行结果"}, {"id": "edge-4", "fromNode": "stock-check", "toNode": "batch-size-check", "color": "4", "label": "4. 成功处理"}, {"id": "edge-5", "fromNode": "batch-size-check", "toNode": "message-trigger", "color": "5", "label": "5. 达到批量"}, {"id": "edge-6", "fromNode": "message-trigger", "toNode": "progress-update", "color": "6", "label": "6. 更新进度"}, {"id": "edge-7", "fromNode": "progress-update", "toNode": "batch-consumer", "color": "1", "label": "7. 消费处理"}, {"id": "edge-8", "fromNode": "batch-consumer", "toNode": "database-batch", "color": "2", "label": "8. 批量操作"}, {"id": "edge-9", "fromNode": "database-batch", "toNode": "fail-handling", "color": "3", "label": "9. 异常处理"}, {"id": "edge-10", "fromNode": "fail-handling", "toNode": "cache-update", "color": "4", "label": "10. 缓存更新"}, {"id": "edge-11", "fromNode": "cache-update", "toNode": "completion-check", "color": "5", "label": "11. 完成检查"}, {"id": "edge-12", "fromNode": "completion-check", "toNode": "performance-monitor", "color": "6", "label": "12. 性能监控"}, {"id": "edge-13", "fromNode": "performance-monitor", "toNode": "bit-optimization", "color": "1", "label": "13. 优化统计"}, {"id": "edge-14", "fromNode": "bit-optimization", "toNode": "final-result", "color": "2", "label": "14. 最终结果"}, {"id": "edge-skip", "fromNode": "progress-check", "toNode": "progress-update", "color": "3", "label": "已处理跳过"}, {"id": "edge-fail", "fromNode": "stock-check", "toNode": "fail-handling", "color": "4", "label": "库存不足"}, {"id": "edge-continue", "fromNode": "batch-size-check", "toNode": "progress-update", "color": "5", "label": "未达批量"}]}