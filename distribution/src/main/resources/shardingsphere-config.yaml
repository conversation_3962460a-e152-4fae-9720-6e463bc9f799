# 牛券（oneCoupon）优惠券平台项目
# 版权所有 (C) [2024-至今] [山东流年网络科技有限公司]
# 保留所有权利。
#
# 1. 定义和解释
#    本文件（包括其任何修改、更新和衍生内容）是由[山东流年网络科技有限公司]及相关人员开发的。
#    "软件"指的是与本文件相关的任何代码、脚本、文档和相关的资源。
#
# 2. 使用许可
#    本软件的使用、分发和解释均受中华人民共和国法律的管辖。只有在遵守以下条件的前提下，才允许使用和分发本软件：
#    a. 未经[山东流年网络科技有限公司]的明确书面许可，不得对本软件进行修改、复制、分发、出售或出租。
#    b. 任何未授权的复制、分发或修改都将被视为侵犯[山东流年网络科技有限公司]的知识产权。
#
# 3. 免责声明
#    本软件按"原样"提供，没有任何明示或暗示的保证，包括但不限于适销性、特定用途的适用性和非侵权性的保证。
#    在任何情况下，[山东流年网络科技有限公司]均不对任何直接、间接、偶然、特殊、典型或间接的损害（包括但不限于采购替代商品或服务；使用、数据或利润损失）承担责任。
#
# 4. 侵权通知与处理
#    a. 如果[山东流年网络科技有限公司]发现或收到第三方通知，表明存在可能侵犯其知识产权的行为，公司将采取必要的措施以保护其权利。
#    b. 对于任何涉嫌侵犯知识产权的行为，[山东流年网络科技有限公司]可能要求侵权方立即停止侵权行为，并采取补救措施，包括但不限于删除侵权内容、停止侵权产品的分发等。
#    c. 如果侵权行为持续存在或未能得到妥善解决，[山东流年网络科技有限公司]保留采取进一步法律行动的权利，包括但不限于发出警告信、提起民事诉讼或刑事诉讼。
#
# 5. 其他条款
#    a. [山东流年网络科技有限公司]保留随时修改这些条款的权利。
#    b. 如果您不同意这些条款，请勿使用本软件。
#
# 未经[山东流年网络科技有限公司]的明确书面许可，不得使用此文件的任何部分。
#
# 本软件受到[山东流年网络科技有限公司]及其许可人的版权保护。

# 数据源集合
dataSources:
  # 自定义数据源名称，可以是 ds_0 也可以叫 datasource_0 都可以
  ds_0:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ******************************************************************************************************************************************************************
    username: root
    password: root
  ds_1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: ******************************************************************************************************************************************************************
    username: root
    password: root

rules:
  - !SHARDING
    tables: # 需要分片的数据库表集合
      t_coupon_template: # 优惠券模板表
        # 真实存在数据库中的物理表
        actualDataNodes: ds_${0..1}.t_coupon_template_${0..15}
        databaseStrategy: # 分库策略
          standard: # 单分片键分库
            shardingColumn: shop_number # 分片键
            shardingAlgorithmName: coupon_template_database_mod # 库分片算法名称，对应 rules[0].shardingAlgorithms
        tableStrategy: # 分表策略
          standard: # 单分片键分表
            shardingColumn: shop_number # 分片键
            shardingAlgorithmName: coupon_template_table_mod # 表分片算法名称，对应 rules[0].shardingAlgorithms
      t_user_coupon:
        actualDataNodes: ds_${0..1}.t_user_coupon_${0..31}
        databaseStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: user_coupon_database_mod
        tableStrategy:
          standard:
            shardingColumn: user_id
            shardingAlgorithmName: user_coupon_table_mod
    shardingAlgorithms: # 分片算法定义集合
      coupon_template_database_mod: # 优惠券分库算法定义
        type: CLASS_BASED # 根据自定义库分片算法类进行分片
        props: # 分片相关属性
          # 自定义库分片算法Class
          algorithmClassName: com.nageoffer.onecoupon.distribution.dao.sharding.DBHashModShardingAlgorithm
          sharding-count: 16 # 分片总数量
          strategy: standard # 分片类型，单字段分片
      coupon_template_table_mod: # 优惠券分表算法定义
        type: CLASS_BASED # 根据自定义库分片算法类进行分片
        props: # 分片相关属性
          # 自定义表分片算法Class
          algorithmClassName: com.nageoffer.onecoupon.distribution.dao.sharding.TableHashModShardingAlgorithm
          strategy: standard # 分片类型，单字段分片
      user_coupon_database_mod:
        type: CLASS_BASED
        props:
          algorithmClassName: com.nageoffer.onecoupon.distribution.dao.sharding.DBHashModShardingAlgorithm
          sharding-count: 32
          strategy: standard
      user_coupon_table_mod:
        type: CLASS_BASED
        props:
          algorithmClassName: com.nageoffer.onecoupon.distribution.dao.sharding.TableHashModShardingAlgorithm
          strategy: standard

props:
  # 配置 ShardingSphere 默认打印 SQL 执行语句
  sql-show: true
