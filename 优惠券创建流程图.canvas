{"nodes": [{"id": "start-node", "type": "text", "x": 100, "y": 50, "width": 150, "height": 80, "text": "# 开始\n\n商家登录后管系统\n准备创建优惠券", "color": "1"}, {"id": "form-submit-node", "type": "text", "x": 300, "y": 50, "width": 200, "height": 100, "text": "# 表单提交\n\n**提交内容：**\n- 优惠券名称\n- 折扣类型和金额\n- 使用条件\n- 有效期设置\n- 发行数量", "color": "2"}, {"id": "duplicate-check-node", "type": "text", "x": 550, "y": 50, "width": 200, "height": 100, "text": "# 防重复提交检查\n\n**@NoDuplicateSubmit**\n- 生成分布式锁Key\n- 尝试获取Redisson锁\n- 失败则抛出重复提交异常", "color": "3"}, {"id": "param-validation-node", "type": "text", "x": 100, "y": 200, "width": 200, "height": 120, "text": "# 参数验证\n\n**责任链模式验证：**\n- 基础参数验证器\n- 业务规则验证器\n- 权限验证器\n- 库存验证器\n\n高内聚、低耦合设计", "color": "4"}, {"id": "validation-success-node", "type": "text", "x": 350, "y": 200, "width": 150, "height": 80, "text": "# 验证通过\n\n所有验证器\n执行成功", "color": "5"}, {"id": "validation-fail-node", "type": "text", "x": 350, "y": 350, "width": 150, "height": 80, "text": "# 验证失败\n\n返回具体\n错误信息", "color": "6"}, {"id": "sharding-storage-node", "type": "text", "x": 550, "y": 200, "width": 200, "height": 120, "text": "# 分库分表存储\n\n**ShardingSphere：**\n- 根据商家ID分库\n- 根据模板ID分表\n- 提升存储和查询效率\n- 支持大量商家数据", "color": "1"}, {"id": "bizlog-record-node", "type": "text", "x": 100, "y": 400, "width": 200, "height": 100, "text": "# 操作日志记录\n\n**BizLog框架：**\n- 记录操作人员\n- 记录操作时间\n- 记录操作内容\n- 确保行为留痕", "color": "2"}, {"id": "cache-update-node", "type": "text", "x": 350, "y": 400, "width": 200, "height": 100, "text": "# 缓存更新\n\n**Redis缓存：**\n- 更新优惠券模板缓存\n- 设置布隆过滤器\n- 预热热点数据\n- 提升查询性能", "color": "3"}, {"id": "success-response-node", "type": "text", "x": 600, "y": 400, "width": 150, "height": 80, "text": "# 创建成功\n\n返回优惠券\n模板ID", "color": "4"}, {"id": "error-response-node", "type": "text", "x": 550, "y": 350, "width": 150, "height": 80, "text": "# 创建失败\n\n返回错误信息\n回滚操作", "color": "6"}], "edges": [{"id": "edge-1", "fromNode": "start-node", "toNode": "form-submit-node", "color": "1", "label": "1. 填写表单"}, {"id": "edge-2", "fromNode": "form-submit-node", "toNode": "duplicate-check-node", "color": "2", "label": "2. 提交请求"}, {"id": "edge-3", "fromNode": "duplicate-check-node", "toNode": "param-validation-node", "color": "3", "label": "3. 通过防重检查"}, {"id": "edge-4", "fromNode": "param-validation-node", "toNode": "validation-success-node", "color": "4", "label": "4. 验证成功"}, {"id": "edge-5", "fromNode": "param-validation-node", "toNode": "validation-fail-node", "color": "5", "label": "5. 验证失败"}, {"id": "edge-6", "fromNode": "validation-success-node", "toNode": "sharding-storage-node", "color": "6", "label": "6. 执行存储"}, {"id": "edge-7", "fromNode": "sharding-storage-node", "toNode": "bizlog-record-node", "color": "1", "label": "7. 记录日志"}, {"id": "edge-8", "fromNode": "bizlog-record-node", "toNode": "cache-update-node", "color": "2", "label": "8. 更新缓存"}, {"id": "edge-9", "fromNode": "cache-update-node", "toNode": "success-response-node", "color": "3", "label": "9. 返回成功"}, {"id": "edge-10", "fromNode": "validation-fail-node", "toNode": "error-response-node", "color": "4", "label": "返回错误"}, {"id": "edge-11", "fromNode": "sharding-storage-node", "toNode": "error-response-node", "color": "5", "label": "存储失败"}]}