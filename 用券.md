# 牛券优惠券系统用券功能详解

## 🎯 优惠券使用的时机

### 1. 主要使用场景：订单结算
优惠券的核心使用场景是在**用户下单结算时**，具体时机：

```
用户购物车 → 确认订单页面 → 查询可用优惠券 → 选择优惠券 → 计算折扣 → 支付 → 核销优惠券
```

### 2. 触发优惠券查询的时点

`CouponQueryServiceImpl.listQueryUserCoupons()`方法是优惠券使用的入口：

```java
@Override
public QueryCouponsRespDTO listQueryUserCoupons(QueryCouponsReqDTO requestParam) {
    // Step 1: 获取 Redis 中的用户优惠券列表
    Set<String> rangeUserCoupons = stringRedisTemplate.opsForZSet().range(
            String.format(USER_COUPON_TEMPLATE_LIST_KEY, UserContext.getUserId()), 0, -1);
    
    if (rangeUserCoupons == null || rangeUserCoupons.isEmpty()) {
        return QueryCouponsRespDTO.builder()
                .availableCouponList(new ArrayList<>())
                .notAvailableCouponList(new ArrayList<>())
                .build();
    }
}
```

**调用时机**：
- 用户进入订单确认页面时
- 用户在结算页面切换优惠券时
- 订单金额或商品发生变化时

## 🔍 优惠券可用性判断实现

### 核心判断逻辑

```java
switch (each.getType()) {
    case 0: // 立减券 - 无门槛直接可用
        resultQueryCouponDetail.setCouponAmount(maximumDiscountAmount);
        availableCouponList.add(resultQueryCouponDetail);
        break;
    case 1: // 满减券 - 需要满足金额门槛
        // orderAmount 大于或等于 termsOfUse
        if (requestParam.getOrderAmount().compareTo(jsonObject.getBigDecimal("termsOfUse")) >= 0) {
            resultQueryCouponDetail.setCouponAmount(maximumDiscountAmount);
            availableCouponList.add(resultQueryCouponDetail);
        } else {
            notAvailableCouponList.add(resultQueryCouponDetail);
        }
        break;
    case 2: // 折扣券 - 需要满足商品和金额条件
        if (couponGoods.getGoodsAmount().compareTo(jsonObject.getBigDecimal("termsOfUse")) >= 0) {
            BigDecimal discountRate = jsonObject.getBigDecimal("discountRate");
            resultQueryCouponDetail.setCouponAmount(couponGoods.getGoodsAmount().multiply(discountRate));
            availableCouponList.add(resultQueryCouponDetail);
        } else {
            notAvailableCouponList.add(resultQueryCouponDetail);
        }
        break;
}
```

### 可用性判断的多个维度

**1. 优惠券类型适配**：
- **立减券（type=0）**：无门槛，直接可用
- **满减券（type=1）**：订单总金额需达到使用门槛
- **折扣券（type=2）**：特定商品金额需达到门槛

**2. 商品适用范围检查**：
```java
// 处理商品专属优惠券
if (StrUtil.isNotBlank(each.getGoods())) {
    // 检查优惠券是否适用于当前商品
    List<String> applicableGoods = Arrays.asList(each.getGoods().split(","));
    boolean isApplicable = requestParam.getGoodsList().stream()
        .anyMatch(goods -> applicableGoods.contains(goods.getGoodsNumber()));
    
    if (!isApplicable) {
        notAvailableCouponList.add(each);
        continue;
    }
}
```

**3. 有效期检查**：
```java
// 检查优惠券是否在有效期内
Date now = new Date();
if (now.before(coupon.getValidStartTime()) || now.after(coupon.getValidEndTime())) {
    notAvailableCouponList.add(coupon);
    continue;
}
```

**4. 状态检查**：
```java
// 只有未使用状态的优惠券才能被使用
if (!Objects.equals(userCoupon.getStatus(), UserCouponStatusEnum.UNUSED.getCode())) {
    continue; // 跳过已使用、已过期、已锁定的优惠券
}
```

## ⚡ 性能优化的查询实现

### 1. 异步并行处理

```java
// Step 2: 使用 CompletableFuture 异步并行处理两种类型的优惠券
CompletableFuture<Void> emptyGoodsTask = CompletableFuture.runAsync(() -> {
    // 处理全店通用优惠券（goods字段为空）
    processEmptyGoodsCoupons(goodsEmptyList, requestParam, availableCouponList, notAvailableCouponList);
}, executorService);

CompletableFuture<Void> notEmptyGoodsTask = CompletableFuture.runAsync(() -> {
    // 处理商品专属优惠券（goods字段不为空）
    Map<String, QueryCouponGoodsReqDTO> goodsRequestMap = requestParam.getGoodsList().stream()
            .collect(Collectors.toMap(QueryCouponGoodsReqDTO::getGoodsNumber, Function.identity()));
    processNonEmptyGoodsCoupons(goodsNotEmptyList, goodsRequestMap, availableCouponList, notAvailableCouponList);
}, executorService);

// Step 3: 等待两个异步任务完成
CompletableFuture.allOf(emptyGoodsTask, notEmptyGoodsTask).join();
```

**性能提升**：
- 全店通用优惠券和商品专属优惠券并行处理
- 减少总处理时间约30-50%

### 2. Redis Pipeline批量查询

```java
// 构建 Redis Key 列表
List<String> couponTemplateIds = rangeUserCoupons.stream()
        .map(each -> StrUtil.split(each, "_").get(0))
        .map(each -> redisDistributedProperties.getPrefix() + String.format(COUPON_TEMPLATE_KEY, each))
        .toList();

// 使用 Pipeline 批量查询，减少网络交互
List<Object> couponTemplateList = stringRedisTemplate.executePipelined((RedisCallback<String>) connection -> {
    couponTemplateIds.forEach(each -> connection.hashCommands().hGetAll(each.getBytes()));
    return null;
});
```

**性能提升**：
- 100个优惠券模板查询从100次网络往返减少到1次
- 网络交互性能提升99%

### 3. 智能排序优化

```java
// Step 4: 与业内标准一致，按最终优惠力度从大到小排序
availableCouponList.sort((c1, c2) -> c2.getCouponAmount().compareTo(c1.getCouponAmount()));
```

**用户体验优化**：
- 优惠力度最大的优惠券排在前面
- 引导用户选择最优惠券

## 🔄 优惠券使用的完整流程

### 第一阶段：查询可用优惠券
```java
// 1. 从Redis ZSet获取用户优惠券列表
// 2. Pipeline批量查询优惠券模板信息
// 3. 异步并行处理不同类型优惠券
// 4. 按优惠力度排序返回
```

### 第二阶段：用户选择优惠券
```java
// 前端展示可用优惠券列表
// 用户选择要使用的优惠券
// 实时计算折扣后的订单金额
```

### 第三阶段：创建结算单并锁定优惠券
```java
// Engine模块：UserCouponService.createPaymentRecord()
// 1. 分布式锁保护
// 2. 创建CouponSettlement记录
// 3. 优惠券状态：UNUSED → LOCKING
// 4. 从Redis缓存中移除
```

### 第四阶段：支付成功后核销优惠券
```java
// Engine模块：UserCouponService.processPayment()
// 1. 结算单状态：锁定 → 已支付
// 2. 优惠券状态：LOCKING → USED
// 3. 编程式事务保证一致性
```

## 🛡️ 使用过程中的控制机制

### 1. 并发控制
```java
// 分布式锁防止同一优惠券被并发使用
RLock lock = redissonClient.getLock(
    String.format(LOCK_COUPON_SETTLEMENT_KEY, couponId)
);
```

### 2. 状态控制
```java
// 严格的状态流转控制
UNUSED → LOCKING → USED
   ↓        ↓
EXPIRED  REVOKED
```

### 3. 幂等性控制
```java
// 防止重复使用同一优惠券
@NoMQDuplicateConsume(
    keyPrefix = "coupon:settlement:",
    key = "#couponId"
)
```

### 4. 异常场景处理
```java
// 订单取消：LOCKING → UNUSED
// 支付失败：LOCKING → UNUSED  
// 退款处理：USED → UNUSED
```

## 📊 使用统计与监控

### 1. 关键指标监控
```java
// 优惠券使用率
@Timer(name = "coupon.usage.rate")
public void trackCouponUsage() { ... }

// 查询性能监控
@Timer(name = "coupon.query.performance")
public QueryCouponsRespDTO listQueryUserCoupons() { ... }
```

### 2. 业务数据统计
```java
// 统计不同类型优惠券的使用情况
// 分析用户优惠券选择偏好
// 监控系统性能指标
```

## 📝 总结

项目中优惠券的使用实现体现了企业级系统的设计精髓：

1. **精确的时机控制**：在订单结算阶段触发使用
2. **智能的可用性判断**：多维度验证确保准确性
3. **高性能的查询实现**：异步并行+Pipeline批量优化
4. **严格的状态管理**：完整的状态流转控制
5. **可靠的并发处理**：分布式锁+事务保证一致性
6. **完善的异常处理**：支持各种异常场景的恢复

这套设计不仅保证了优惠券使用的正确性，还具备了优秀的性能表现和用户体验，是学习电商系统优惠券功能的优秀范例！
