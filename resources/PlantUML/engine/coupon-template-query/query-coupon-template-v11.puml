@startuml
skinparam backgroundColor #F4F4F4
skinparam shadowing true
skinparam handwritten true
skinparam sequence {
    ActorBackgroundColor #C0C0C0
    LifeLineBackgroundColor #F0F0F0
    LifeLineBorderColor #3498DB
    ParticipantBorderColor #3498DB
    ParticipantBackgroundColor #E6F7FF
    ArrowColor #2980B9
    ArrowFontColor #2C3E50
    FontColor #2C3E50
    FontSize 12
}

actor User as "用户"
participant Service as "Coupon Service"
participant "布隆过滤器" as BloomFilter
participant DistributedLock as "分布式锁"
database Redis as "Redis缓存"
database MySQL as "MySQL数据库"

title 缓存穿透防护的时序图（布隆过滤器 + 空值缓存 + 分布式锁）

User -> Service: 请求优惠券模板信息
Service -> Redis: 查询缓存 (根据优惠券模板ID)

alt 缓存中无数据
    Redis -> Service: 返回空
    Service -> BloomFilter: 查询布隆过滤器 (检查模板ID)
    alt 布隆过滤器中不存在
        BloomFilter -> Service: 返回 "不存在"
        Service -[#red]> User: 返回 "失败：无效的优惠券模板ID"
    else 布隆过滤器中存在
        BloomFilter -> Service: 返回 "可能存在"
        Service -> Redis: 检查缓存是否存在空值
        alt 存在缓存空值
            Redis -> Service: 返回空值
            Service -[#red]> User: 返回 "失败：无效的优惠券模板ID"
        else 不存在缓存空值
            Service -> DistributedLock: 请求分布式锁
            alt 锁可用
                DistributedLock -> Redis: 获取锁
                Redis --> DistributedLock: 锁获取成功
                DistributedLock -> MySQL: 查询数据库 (根据模板ID和店铺编号)
                alt 数据库中无数据
                    MySQL -> DistributedLock: 返回空
                    DistributedLock -> Redis: 缓存空结果 (短时间过期)
                    DistributedLock -> Redis: 释放锁
                    DistributedLock -[#red]> User: 返回 "失败：无效的优惠券模板ID"
                else 数据库中有数据
                    MySQL --> DistributedLock: 返回数据库数据
                    DistributedLock -> Redis: 将数据存入缓存
                    DistributedLock -> Redis: 释放锁
                    DistributedLock --> User: 返回数据库数据
                end
            else 锁不可用
                DistributedLock -> User: 等待或返回错误信息
            end
        end
    end
else 缓存中有数据
    Redis -> Service: 返回缓存数据
    Service --> User: 返回缓存数据
end

@enduml
