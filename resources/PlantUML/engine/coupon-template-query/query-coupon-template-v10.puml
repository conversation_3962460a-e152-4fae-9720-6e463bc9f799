@startuml
skinparam backgroundColor #F4F4F4
skinparam shadowing true
skinparam handwritten true
skinparam sequence {
    ActorBackgroundColor #C0C0C0
    LifeLineBackgroundColor #F0F0F0
    LifeLineBorderColor #3498DB
    ParticipantBorderColor #3498DB
    ParticipantBackgroundColor #E6F7FF
    ArrowColor #2980B9
    ArrowFontColor #2C3E50
    FontColor #2C3E50
    FontSize 12
}

actor User as "用户"
participant Service as "Coupon Service"
participant "布隆过滤器" as BloomFilter
database Redis as "Redis缓存"
database MySQL as "MySQL数据库"

title 缓存穿透防护的时序图（布隆过滤器方案）

User -> Service: 请求优惠券模板信息
Service -> Redis: 查询缓存 (根据优惠券模板ID)

alt 缓存中无数据
    Redis -> Service: 返回空
    Service -> BloomFilter: 查询布隆过滤器 (检查模板ID)
    alt 布隆过滤器中不存在
        BloomFilter -> Service: 返回 "不存在"
        Service -[#red]> User: 返回 "错误响应：无效的优惠券模板ID"
    else 布隆过滤器中存在
        BloomFilter -> Service: 返回 "可能存在"
        Service -> MySQL: 查询数据库 (根据模板ID和店铺编号)
        alt 数据库中无数据
            MySQL -> Service: 返回空
            Service -[#red]> User: 返回 "错误响应：无效的优惠券模板ID"
        else 数据库中有数据
            MySQL --> Service: 返回数据库数据
            Service -> Redis: 将数据存入缓存
            Service --> User: 返回数据库数据
        end
    end
else 缓存中有数据
    Redis -> Service: 返回缓存数据
    Service --> User: 返回缓存数据
end

@enduml
