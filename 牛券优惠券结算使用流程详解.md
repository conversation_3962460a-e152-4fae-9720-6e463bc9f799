# 牛券优惠券结算使用流程详解

## 🎯 概述

牛券优惠券系统在结算使用优惠券时，采用了一套完整的状态管理和流程控制机制，确保优惠券使用的准确性、一致性和可靠性。整个流程涉及**结算模块**和**引擎模块**的协同工作。

## 🏗️ 核心架构

### 模块职责分工

```
结算模块 (Settlement)          引擎模块 (Engine)
├── 优惠券查询与筛选            ├── 优惠券状态管理
├── 折扣金额计算               ├── 结算单创建与管理  
├── 可用性判断                 ├── 支付处理
└── 策略模式计算               └── 退款处理
```

## 🔄 完整结算流程

### 第一阶段：优惠券查询与筛选

#### 1.1 查询用户可用优惠券

<augment_code_snippet path="settlement/src/main/java/com/nageoffer/onecoupon/settlement/service/impl/CouponQueryServiceImpl.java" mode="EXCERPT">
```java
@Override
public QueryCouponsRespDTO listQueryUserCoupons(QueryCouponsReqDTO requestParam) {
    // Step 1: 获取 Redis 中的用户优惠券列表
    Set<String> rangeUserCoupons = stringRedisTemplate.opsForZSet().range(
            String.format(USER_COUPON_TEMPLATE_LIST_KEY, UserContext.getUserId()), 0, -1);
    
    if (rangeUserCoupons == null || rangeUserCoupons.isEmpty()) {
        return QueryCouponsRespDTO.builder()
                .availableCouponList(new ArrayList<>())
                .notAvailableCouponList(new ArrayList<>())
                .build();
    }
```
</augment_code_snippet>

**技术要点**：
- **Redis ZSet存储**：用户优惠券按时间排序存储
- **批量查询优化**：使用Pipeline减少网络交互
- **异步处理**：CompletableFuture并行处理不同类型优惠券

#### 1.2 优惠券可用性判断

<augment_code_snippet path="settlement/src/main/java/com/nageoffer/onecoupon/settlement/service/impl/CouponQueryServiceImpl.java" mode="EXCERPT">
```java
switch (each.getType()) {
    case 0: // 立减券
        resultQueryCouponDetail.setCouponAmount(maximumDiscountAmount);
        availableCouponList.add(resultQueryCouponDetail);
        break;
    case 1: // 满减券
        // orderAmount 大于或等于 termsOfUse
        if (requestParam.getOrderAmount().compareTo(jsonObject.getBigDecimal("termsOfUse")) >= 0) {
            resultQueryCouponDetail.setCouponAmount(maximumDiscountAmount);
            availableCouponList.add(resultQueryCouponDetail);
        } else {
            notAvailableCouponList.add(resultQueryCouponDetail);
        }
        break;
    case 2: // 折扣券
        if (couponGoods.getGoodsAmount().compareTo(jsonObject.getBigDecimal("termsOfUse")) >= 0) {
            BigDecimal discountRate = jsonObject.getBigDecimal("discountRate");
            resultQueryCouponDetail.setCouponAmount(couponGoods.getGoodsAmount().multiply(discountRate));
            availableCouponList.add(resultQueryCouponDetail);
        } else {
            notAvailableCouponList.add(resultQueryCouponDetail);
        }
        break;
}
```
</augment_code_snippet>

### 第二阶段：折扣金额计算

#### 2.1 策略模式计算折扣

<augment_code_snippet path="settlement/src/main/java/com/nageoffer/onecoupon/settlement/service/CouponCalculationService.java" mode="EXCERPT">
```java
@Service
public class CouponCalculationService {
    
    /**
     * 计算优惠金额
     * 根据传入的优惠券实例和订单金额，选择相应的计算策略，返回最终的优惠金额
     */
    public BigDecimal calculateDiscount(CouponTemplateDO coupon, BigDecimal orderAmount) {
        CouponCalculationStrategy strategy = CouponFactory.getCouponCalculationStrategy(coupon);
        return strategy.calculateDiscount(coupon, orderAmount);
    }
}
```
</augment_code_snippet>

#### 2.2 具体计算策略实现

**立减券策略**：
<augment_code_snippet path="settlement/src/main/java/com/nageoffer/onecoupon/settlement/service/strategy/FixedDiscountCalculationStrategy.java" mode="EXCERPT">
```java
public class FixedDiscountCalculationStrategy implements CouponCalculationStrategy {
    
    @Override
    public BigDecimal calculateDiscount(CouponTemplateDO template, BigDecimal orderAmount) {
        FixedDiscountCouponDO fixedDiscount = (FixedDiscountCouponDO) template;
        return BigDecimal.valueOf(fixedDiscount.getDiscountAmount());
    }
}
```
</augment_code_snippet>

**满减券策略**：
<augment_code_snippet path="settlement/src/main/java/com/nageoffer/onecoupon/settlement/service/strategy/ThresholdCalculationStrategy.java" mode="EXCERPT">
```java
public class ThresholdCalculationStrategy implements CouponCalculationStrategy {
    
    @Override
    public BigDecimal calculateDiscount(CouponTemplateDO template, BigDecimal orderAmount) {
        ThresholdCouponDO thresholdDiscount = (ThresholdCouponDO) template;
        if (orderAmount.compareTo(BigDecimal.valueOf(thresholdDiscount.getThresholdAmount())) >= 0) {
            return BigDecimal.valueOf(thresholdDiscount.getDiscountAmount());
        }
        return BigDecimal.ZERO;
    }
}
```
</augment_code_snippet>

### 第三阶段：优惠券锁定与结算单创建

#### 3.1 创建结算单并锁定优惠券

<augment_code_snippet path="engine/src/main/java/com/nageoffer/onecoupon/engine/service/impl/UserCouponServiceImpl.java" mode="EXCERPT">
```java
@Override
public void createPaymentRecord(CouponCreatePaymentReqDTO requestParam) {
    RLock lock = redissonClient.getLock(String.format(EngineRedisConstant.LOCK_COUPON_SETTLEMENT_KEY, requestParam.getCouponId()));
    boolean tryLock = lock.tryLock();
    if (!tryLock) {
        throw new ClientException("正在创建优惠券结算单，请稍候再试");
    }
    
    try {
        // 通过编程式事务减小事务范围
        transactionTemplate.executeWithoutResult(status -> {
            try {
                // 创建优惠券结算单记录
                CouponSettlementDO couponSettlementDO = CouponSettlementDO.builder()
                        .orderId(requestParam.getOrderId())
                        .couponId(requestParam.getCouponId())
                        .userId(Long.parseLong(UserContext.getUserId()))
                        .status(0) // 0：锁定状态
                        .build();
                couponSettlementMapper.insert(couponSettlementDO);
                
                // 变更用户优惠券状态为锁定
                LambdaUpdateWrapper<UserCouponDO> userCouponUpdateWrapper = Wrappers.lambdaUpdate(UserCouponDO.class)
                        .eq(UserCouponDO::getId, requestParam.getCouponId())
                        .eq(UserCouponDO::getUserId, Long.parseLong(UserContext.getUserId()))
                        .eq(UserCouponDO::getStatus, UserCouponStatusEnum.UNUSED.getCode());
                UserCouponDO updateUserCouponDO = UserCouponDO.builder()
                        .status(UserCouponStatusEnum.LOCKING.getCode())
                        .build();
                userCouponMapper.update(updateUserCouponDO, userCouponUpdateWrapper);
            } catch (Exception ex) {
                log.error("创建优惠券结算单失败", ex);
                status.setRollbackOnly();
                throw ex;
            }
        });
        
        // 从用户可用优惠券列表中删除优惠券
        String userCouponItemCacheKey = StrUtil.builder()
                .append(userCouponDO.getCouponTemplateId())
                .append("_")
                .append(userCouponDO.getId())
                .toString();
        stringRedisTemplate.opsForZSet().remove(String.format(USER_COUPON_TEMPLATE_LIST_KEY, UserContext.getUserId()), userCouponItemCacheKey);
    } finally {
        lock.unlock();
    }
}
```
</augment_code_snippet>

### 第四阶段：支付处理与优惠券核销

#### 4.1 处理支付并核销优惠券

<augment_code_snippet path="engine/src/main/java/com/nageoffer/onecoupon/engine/service/impl/UserCouponServiceImpl.java" mode="EXCERPT">
```java
@Override
public void processPayment(CouponProcessPaymentReqDTO requestParam) {
    RLock lock = redissonClient.getLock(String.format(EngineRedisConstant.LOCK_COUPON_SETTLEMENT_KEY, requestParam.getCouponId()));
    boolean tryLock = lock.tryLock();
    if (!tryLock) {
        throw new ClientException("正在核销优惠券结算单，请稍候再试");
    }
    
    // 通过编程式事务减小事务范围
    transactionTemplate.executeWithoutResult(status -> {
        try {
            // 变更优惠券结算单状态为已支付
            LambdaUpdateWrapper<CouponSettlementDO> couponSettlementUpdateWrapper = Wrappers.lambdaUpdate(CouponSettlementDO.class)
                    .eq(CouponSettlementDO::getCouponId, requestParam.getCouponId())
                    .eq(CouponSettlementDO::getUserId, Long.parseLong(UserContext.getUserId()))
                    .eq(CouponSettlementDO::getStatus, 0);
            CouponSettlementDO couponSettlementDO = CouponSettlementDO.builder()
                    .status(2) // 2：已支付
                    .build();
            int couponSettlementUpdated = couponSettlementMapper.update(couponSettlementDO, couponSettlementUpdateWrapper);
            
            // 变更用户优惠券状态为已使用
            LambdaUpdateWrapper<UserCouponDO> userCouponUpdateWrapper = Wrappers.lambdaUpdate(UserCouponDO.class)
                    .eq(UserCouponDO::getId, requestParam.getCouponId())
                    .eq(UserCouponDO::getUserId, Long.parseLong(UserContext.getUserId()))
                    .eq(UserCouponDO::getStatus, UserCouponStatusEnum.LOCKING.getCode());
            UserCouponDO userCouponDO = UserCouponDO.builder()
                    .status(UserCouponStatusEnum.USED.getCode())
                    .build();
            int userCouponUpdated = userCouponMapper.update(userCouponDO, userCouponUpdateWrapper);
        } catch (Exception ex) {
            log.error("核销优惠券结算单失败", ex);
            status.setRollbackOnly();
            throw ex;
        } finally {
            lock.unlock();
        }
    });
}
```
</augment_code_snippet>

### 第五阶段：退款处理与状态回滚

#### 5.1 处理退款并恢复优惠券

<augment_code_snippet path="engine/src/main/java/com/nageoffer/onecoupon/engine/service/impl/UserCouponServiceImpl.java" mode="EXCERPT">
```java
@Override
public void processRefund(CouponProcessRefundReqDTO requestParam) {
    RLock lock = redissonClient.getLock(String.format(EngineRedisConstant.LOCK_COUPON_SETTLEMENT_KEY, requestParam.getCouponId()));
    boolean tryLock = lock.tryLock();
    if (!tryLock) {
        throw new ClientException("正在执行优惠券退款，请稍候再试");
    }
    
    try {
        transactionTemplate.executeWithoutResult(status -> {
            try {
                // 变更优惠券结算单状态为已退款
                CouponSettlementDO couponSettlementDO = CouponSettlementDO.builder()
                        .status(3) // 3：已退款
                        .build();
                couponSettlementMapper.update(couponSettlementDO, couponSettlementUpdateWrapper);
                
                // 变更用户优惠券状态为未使用
                UserCouponDO userCouponDO = UserCouponDO.builder()
                        .status(UserCouponStatusEnum.UNUSED.getCode())
                        .build();
                userCouponMapper.update(userCouponDO, userCouponUpdateWrapper);
            } catch (Exception ex) {
                log.error("执行优惠券结算单退款失败", ex);
                status.setRollbackOnly();
                throw ex;
            }
        });
        
        // 查询出来优惠券再放回缓存
        UserCouponDO userCouponDO = userCouponMapper.selectOne(Wrappers.lambdaQuery(UserCouponDO.class)
                .eq(UserCouponDO::getUserId, Long.parseLong(UserContext.getUserId()))
                .eq(UserCouponDO::getId, requestParam.getCouponId())
        );
        String userCouponItemCacheKey = StrUtil.builder()
                .append(userCouponDO.getCouponTemplateId())
                .append("_")
                .append(userCouponDO.getId())
                .toString();
        stringRedisTemplate.opsForZSet().add(String.format(USER_COUPON_TEMPLATE_LIST_KEY, UserContext.getUserId()), userCouponItemCacheKey, userCouponDO.getReceiveTime().getTime());
    } finally {
        lock.unlock();
    }
}
```
</augment_code_snippet>

## 📊 状态流转图

### 优惠券状态流转

```
未使用(UNUSED) → 锁定(LOCKING) → 已使用(USED)
      ↑              ↓              ↓
   退款恢复        取消订单        已过期(EXPIRED)
      ↑              ↓
   已撤回(REVOKED) ← 系统撤回
```

### 结算单状态流转

```
锁定(0) → 已支付(2) → 已退款(3)
   ↓
已取消(1)
```

## 🔧 核心技术特性

### 1. 分布式锁保证并发安全

```java
RLock lock = redissonClient.getLock(String.format(EngineRedisConstant.LOCK_COUPON_SETTLEMENT_KEY, requestParam.getCouponId()));
```

**作用**：
- 防止同一优惠券被并发操作
- 确保状态变更的原子性
- 避免数据不一致问题

### 2. 编程式事务控制

```java
transactionTemplate.executeWithoutResult(status -> {
    // 事务操作
});
```

**优势**：
- 精确控制事务边界
- 减小事务范围提升性能
- 支持事务回滚机制

### 3. 策略模式计算折扣

```java
public static CouponCalculationStrategy getCouponCalculationStrategy(CouponTemplateDO coupon) {
    switch (DiscountTypeEnum.values()[coupon.getType()]) {
        case FIXED_DISCOUNT:
            return new FixedDiscountCalculationStrategy();
        case THRESHOLD_DISCOUNT:
            return new ThresholdCalculationStrategy();
        case DISCOUNT_COUPON:
            return new DiscountCalculationStrategy();
    }
}
```

**特点**：
- 支持多种优惠券类型
- 易于扩展新的计算策略
- 代码结构清晰可维护

### 4. Redis缓存优化

- **ZSet存储**：用户优惠券按时间排序
- **Pipeline批量操作**：减少网络交互
- **缓存同步**：状态变更时及时更新缓存

## 🚀 性能优化亮点

### 1. 异步并行处理

```java
CompletableFuture<Void> emptyGoodsTask = CompletableFuture.runAsync(() -> {
    processEmptyGoodsCoupons(goodsEmptyList, requestParam, availableCouponList, notAvailableCouponList);
}, executorService);

CompletableFuture<Void> notEmptyGoodsTask = CompletableFuture.runAsync(() -> {
    processNonEmptyGoodsCoupons(goodsNotEmptyList, goodsRequestMap, availableCouponList, notAvailableCouponList);
}, executorService);

CompletableFuture.allOf(emptyGoodsTask, notEmptyGoodsTask).join();
```

### 2. 批量查询优化

```java
List<Object> couponTemplateList = stringRedisTemplate.executePipelined((RedisCallback<String>) connection -> {
    couponTemplateIds.forEach(each -> connection.hashCommands().hGetAll(each.getBytes()));
    return null;
});
```

### 3. 智能排序

```java
// 与业内标准一致，按最终优惠力度从大到小排序
availableCouponList.sort((c1, c2) -> c2.getCouponAmount().compareTo(c1.getCouponAmount()));
```

## 📝 总结

牛券优惠券系统的结算使用流程体现了企业级系统设计的精髓：

1. **完整的状态管理**：从查询到核销的全流程状态控制
2. **高并发处理能力**：分布式锁 + 编程式事务保证数据一致性
3. **灵活的计算策略**：策略模式支持多种优惠券类型
4. **优秀的性能表现**：异步处理 + 缓存优化 + 批量操作
5. **可靠的异常处理**：完善的回滚机制和错误恢复

这套设计不仅保证了业务的正确性，还具备了良好的扩展性和维护性，是学习企业级系统设计的优秀范例！
