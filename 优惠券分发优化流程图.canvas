{"nodes": [{"id": "excel-upload-node", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# Excel文件上传\n\n**百万级数据处理：**\n- 商家上传用户Excel\n- 文件大小可达百万行\n- 需要高效解析处理", "color": "1"}, {"id": "async-count-node", "type": "text", "x": 350, "y": 50, "width": 220, "height": 120, "text": "# 异步统计行数\n\n**线程池 + 延时队列：**\n- ExecutorService异步统计\n- 避免阻塞用户请求\n- Redisson延时队列兜底\n- 20秒延时保障机制", "color": "2"}, {"id": "mq-decouple-node", "type": "text", "x": 600, "y": 50, "width": 200, "height": 100, "text": "# 消息队列解耦\n\n**RocketMQ异步处理：**\n- 后管发送分发消息\n- 分发模块消费处理\n- 系统解耦降低依赖", "color": "3"}, {"id": "easyexcel-stream-node", "type": "text", "x": 100, "y": 200, "width": 220, "height": 120, "text": "# EasyExcel流式读取\n\n**内存优化：**\n- 逐行读取避免OOM\n- ReadExcelDistributionListener\n- 边读边处理机制\n- 支持百万级数据", "color": "4"}, {"id": "progress-tracking-node", "type": "text", "x": 350, "y": 200, "width": 220, "height": 120, "text": "# 断点续传机制\n\n**进度跟踪：**\n- Redis记录执行进度\n- 应用宕机可恢复\n- rowCount行号标识\n- 跳过已处理数据", "color": "5"}, {"id": "lua-atomic-node", "type": "text", "x": 600, "y": 200, "width": 220, "height": 120, "text": "# Lua脚本原子操作\n\n**Redis层面优化：**\n- 库存扣减 + 用户记录\n- 原子性保证\n- 减少网络交互\n- 位运算优化返回值", "color": "6"}, {"id": "batch-processing-node", "type": "text", "x": 100, "y": 350, "width": 220, "height": 120, "text": "# 批量处理机制\n\n**性能优化：**\n- 5000条批量保存\n- 减少数据库交互\n- 批量Lua脚本操作\n- 提升处理效率", "color": "1"}, {"id": "optimistic-lock-node", "type": "text", "x": 350, "y": 350, "width": 220, "height": 120, "text": "# 乐观锁防超卖\n\n**并发控制：**\n- 版本号机制\n- 避免库存多扣\n- 自旋重试机制\n- 高并发场景适用", "color": "2"}, {"id": "idempotent-design-node", "type": "text", "x": 600, "y": 350, "width": 220, "height": 120, "text": "# 幂等性设计\n\n**重复处理防护：**\n- 唯一索引约束\n- 捕获重复异常\n- 单条插入兜底\n- 失败记录追踪", "color": "3"}, {"id": "strategy-pattern-node", "type": "text", "x": 100, "y": 500, "width": 220, "height": 120, "text": "# 策略模式通知\n\n**多渠道支持：**\n- 应用推送\n- 短信通知\n- 邮件发送\n- 站内信息", "color": "4"}, {"id": "fail-tracking-node", "type": "text", "x": 350, "y": 500, "width": 220, "height": 120, "text": "# 失败记录追踪\n\n**异常处理：**\n- 失败原因记录\n- Excel导出失败数据\n- 便于问题排查\n- 数据完整性保证", "color": "5"}, {"id": "performance-monitor-node", "type": "text", "x": 600, "y": 500, "width": 220, "height": 120, "text": "# 性能监控优化\n\n**系统监控：**\n- 位运算性能测试\n- 执行时间统计\n- 内存使用监控\n- 吞吐量分析", "color": "6"}], "edges": [{"id": "edge-1", "fromNode": "excel-upload-node", "toNode": "async-count-node", "color": "1", "label": "1. 异步统计"}, {"id": "edge-2", "fromNode": "async-count-node", "toNode": "mq-decouple-node", "color": "2", "label": "2. 消息发送"}, {"id": "edge-3", "fromNode": "mq-decouple-node", "toNode": "easyexcel-stream-node", "color": "3", "label": "3. 消费处理"}, {"id": "edge-4", "fromNode": "easyexcel-stream-node", "toNode": "progress-tracking-node", "color": "4", "label": "4. 进度记录"}, {"id": "edge-5", "fromNode": "progress-tracking-node", "toNode": "lua-atomic-node", "color": "5", "label": "5. 原子操作"}, {"id": "edge-6", "fromNode": "lua-atomic-node", "toNode": "batch-processing-node", "color": "6", "label": "6. 批量处理"}, {"id": "edge-7", "fromNode": "batch-processing-node", "toNode": "optimistic-lock-node", "color": "1", "label": "7. 并发控制"}, {"id": "edge-8", "fromNode": "optimistic-lock-node", "toNode": "idempotent-design-node", "color": "2", "label": "8. 幂等保证"}, {"id": "edge-9", "fromNode": "idempotent-design-node", "toNode": "strategy-pattern-node", "color": "3", "label": "9. 通知发送"}, {"id": "edge-10", "fromNode": "strategy-pattern-node", "toNode": "fail-tracking-node", "color": "4", "label": "10. 异常处理"}, {"id": "edge-11", "fromNode": "fail-tracking-node", "toNode": "performance-monitor-node", "color": "5", "label": "11. 性能监控"}]}