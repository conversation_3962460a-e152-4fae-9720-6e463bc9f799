# 牛券优惠券系统发券功能详解

## 📈 版本演进概述

### V1版本：单条数据分发
- **设计理念**：简单直接，每读取一行Excel就立即处理一个用户
- **适用场景**：小规模发券，用户量较少的情况
- **性能特点**：实时性好，但吞吐量有限

### V2版本：批量分发优化  
- **设计理念**：批量聚合，达到阈值后批量处理
- **适用场景**：大规模发券，百万级用户发放
- **性能特点**：高吞吐量，但有一定延迟

## 🎯 发券的两种实现方式

### 1. 批量发券（Distribution模块）
**场景**：商家通过后台批量给用户发放优惠券

### 2. 用户主动领券（Engine模块）  
**场景**：用户在领券中心或商品页面主动领取优惠券

## 📦 批量发券实现（Distribution模块）

### 发券流程概述
```
商家上传Excel → 创建推送任务 → XXL-Job扫描 → 消息队列异步处理 → EasyExcel解析 → 批量发券
```

### 核心实现代码

**1. 创建推送任务**
```java
// merchant-admin模块
@PostMapping("/api/merchant-admin/coupon-task")
public Result<Void> createCouponTask(@RequestBody CouponTaskCreateReqDTO requestParam) {
    // 创建优惠券推送任务
    couponTaskService.createCouponTask(requestParam);
    return Results.success();
}
```

**2. XXL-Job定时扫描**
```java
@XxlJob(value = "couponTemplateTask")
public void execute() throws Exception {
    long initId = 0;
    Date now = new Date();
    
    while (true) {
        // 获取已到执行时间待执行的优惠券定时分发任务
        List<CouponTaskDO> couponTaskDOList = fetchPendingTasks(initId, now);
        
        if (CollUtil.isEmpty(couponTaskDOList)) {
            break;
        }
        
        // 调用分发服务对用户发送优惠券
        for (CouponTaskDO each : couponTaskDOList) {
            distributeCoupon(each);
        }
    }
}

private void distributeCoupon(CouponTaskDO couponTask) {
    // 修改任务状态为执行中
    CouponTaskDO couponTaskDO = CouponTaskDO.builder()
            .id(couponTask.getId())
            .status(CouponTaskStatusEnum.IN_PROGRESS.getStatus())
            .build();
    couponTaskMapper.updateById(couponTaskDO);
    
    // 通过消息队列发送消息，由分发服务消费
    CouponTaskExecuteEvent couponTaskExecuteEvent = CouponTaskExecuteEvent.builder()
            .couponTaskId(couponTask.getId())
            .build();
    couponTaskActualExecuteProducer.sendMessage(couponTaskExecuteEvent);
}
```

**3. 消息队列消费处理**
```java
@Override
public void onMessage(MessageWrapper<CouponTaskExecuteEvent> messageWrapper) {
    var couponTaskId = messageWrapper.getMessage().getCouponTaskId();
    var couponTaskDO = couponTaskMapper.selectById(couponTaskId);
    
    // 判断优惠券模板发送状态是否为执行中
    if (ObjectUtil.notEqual(couponTaskDO.getStatus(), CouponTaskStatusEnum.IN_PROGRESS.getStatus())) {
        log.warn("推送任务记录状态异常：{}，已终止推送", couponTaskDO.getStatus());
        return;
    }
    
    // 判断优惠券状态是否正确
    var couponTemplateDO = couponTemplateMapper.selectOne(queryWrapper);
    var status = couponTemplateDO.getStatus();
    if (ObjectUtil.notEqual(status, CouponTemplateStatusEnum.ACTIVE.getStatus())) {
        log.error("优惠券模板状态：{}", status);
        return;
    }
    
    // 正式开始执行优惠券推送任务
    ReadExcelDistributionListener readExcelDistributionListener = new ReadExcelDistributionListener(
            couponTaskDO, couponTemplateDO, couponTaskFailMapper, 
            stringRedisTemplate, couponExecuteDistributionProducer
    );
    EasyExcel.read(couponTaskDO.getFileAddress(), CouponTaskExcelObject.class, readExcelDistributionListener)
            .sheet().doRead();
}
```

## 🔍 V1版本：单条分发实现

### 核心处理逻辑
```java
// V1版本的ReadExcelDistributionListener实现
@Override
public void invoke(CouponTaskExcelObject data, AnalysisContext context) {
    // 每读取一行Excel数据就立即处理
    
    // 1. Lua脚本扣减库存
    Long luaResult = stringRedisTemplate.execute(stockDecrementLuaScript, 
            ListUtil.of(couponTemplateKey), data.getUserId());
    
    if (StockDecrementReturnCombinedUtil.extractFirstField(luaResult)) {
        // 2. 立即发送单个用户的发券消息
        CouponDistributionEvent event = CouponDistributionEvent.builder()
                .userId(data.getUserId())
                .couponTemplateId(couponTemplateDO.getId())
                .shopNumber(couponTaskDO.getShopNumber())
                .build();
        
        // 3. 每个用户都发送一条消息
        couponDistributionProducer.sendMessage(event);
        
        // 4. 立即插入数据库
        UserCouponDO userCouponDO = buildUserCoupon(data, couponTemplateDO);
        userCouponMapper.insert(userCouponDO);
        
        // 5. 立即更新用户缓存
        updateUserCouponCache(data.getUserId(), userCouponDO);
    }
    
    ++rowCount;
}
```

### V1版本的消费者处理
```java
// V1版本消费者：处理单个用户发券
@RocketMQMessageListener(topic = "coupon_distribution_v1")
public class CouponDistributionV1Consumer implements RocketMQListener<CouponDistributionEvent> {
    
    @Override
    public void onMessage(CouponDistributionEvent event) {
        // 处理单个用户的发券逻辑
        try {
            // 1. 插入用户优惠券记录
            UserCouponDO userCoupon = UserCouponDO.builder()
                    .userId(event.getUserId())
                    .couponTemplateId(event.getCouponTemplateId())
                    .status(UserCouponStatusEnum.UNUSED.getCode())
                    .receiveTime(new Date())
                    .build();
            userCouponMapper.insert(userCoupon);
            
            // 2. 更新用户优惠券缓存
            String userCouponKey = String.format(USER_COUPON_LIST_KEY, event.getUserId());
            String couponItem = event.getCouponTemplateId() + "_" + userCoupon.getId();
            stringRedisTemplate.opsForZSet().add(userCouponKey, couponItem, System.currentTimeMillis());
            
            // 3. 发送通知（如果需要）
            if (needNotification(event)) {
                sendNotification(event);
            }
            
        } catch (Exception e) {
            log.error("V1单条发券处理失败", e);
            throw e;
        }
    }
}
```

## 🚀 V2版本：批量分发优化

### 核心处理逻辑
```java
@Override
public void invoke(CouponTaskExcelObject data, AnalysisContext context) {
    // V2版本：批量聚合处理
    
    // 1. 执行Lua脚本：扣减库存 + 添加到批量集合
    String batchUserSetKey = String.format(TEMPLATE_TASK_EXECUTE_BATCH_USER_KEY, couponTaskId);
    Map<Object, Object> userRowNumMap = MapUtil.builder()
            .put("userId", data.getUserId())
            .put("rowNum", rowCount + 1)
            .build();
    
    Long combinedResult = stringRedisTemplate.execute(buildLuaScript, 
            ListUtil.of(couponTemplateKey, batchUserSetKey), 
            JSON.toJSONString(userRowNumMap));
    
    // 2. 检查库存扣减结果
    boolean stockSuccess = StockDecrementReturnCombinedUtil.extractFirstField(combinedResult);
    if (!stockSuccess) {
        // 记录失败但不阻塞处理
        recordFailure(data, "优惠券模板无库存");
        return;
    }
    
    // 3. 获取当前批量集合大小
    int batchUserSetSize = StockDecrementReturnCombinedUtil.extractSecondField(combinedResult.intValue());
    
    // 4. 达到批量阈值或有通知需求时才发送消息
    if (batchUserSetSize >= BATCH_USER_COUPON_SIZE || StrUtil.isNotBlank(couponTaskDO.getNotifyType())) {
        CouponTemplateDistributionEvent event = CouponTemplateDistributionEvent.builder()
                .userId(data.getUserId())
                .couponTaskId(couponTaskId)
                .shopNumber(couponTaskDO.getShopNumber())
                .couponTemplateId(couponTemplateDO.getId())
                .build();
        
        // 5. 发送批量处理消息
        couponExecuteDistributionProducer.sendMessage(event);
    }
    
    // 6. 更新处理进度
    stringRedisTemplate.opsForValue().set(templateTaskExecuteProgressKey, String.valueOf(rowCount));
    ++rowCount;
}
```

### V2版本的Lua脚本优化
```lua
-- V2版本Lua脚本：stock_decrement_and_batch_save_user_record.lua
local templateKey = KEYS[1]
local batchUserSetKey = KEYS[2]
local userRowData = ARGV[1]

-- 解析用户数据
local userData = cjson.decode(userRowData)
local userId = userData.userId
local rowNum = userData.rowNum

-- 检查库存
local stock = redis.call('HGET', templateKey, 'stock')
if not stock or tonumber(stock) <= 0 then
    -- 库存不足，返回失败标识
    return redis.call('BITFIELD', 'temp', 'SET', 'u1', '0', '0', 'SET', 'u31', '1', '0')
end

-- 扣减库存
redis.call('HINCRBY', templateKey, 'stock', -1)

-- 添加用户到批量处理集合
redis.call('HSET', batchUserSetKey, userId, rowNum)

-- 获取批量集合大小
local batchSize = redis.call('HLEN', batchUserSetKey)

-- 返回成功标识和批量大小（使用位运算组合）
return redis.call('BITFIELD', 'temp', 'SET', 'u1', '0', '1', 'SET', 'u31', '1', batchSize)
```
