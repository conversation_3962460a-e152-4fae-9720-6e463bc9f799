{"nodes": [{"id": "user-order-start", "type": "text", "x": 100, "y": 50, "width": 200, "height": 100, "text": "# 用户下单\n\n**触发场景：**\n- 用户选择商品\n- 进入结算页面\n- 选择使用优惠券", "color": "1"}, {"id": "query-available-coupons", "type": "text", "x": 350, "y": 50, "width": 220, "height": 120, "text": "# 查询可用优惠券\n\n**Settlement模块：**\n- Redis ZSet获取用户优惠券\n- Pipeline批量查询模板信息\n- 异步并行处理\n- 按优惠力度排序", "color": "2"}, {"id": "coupon-availability-check", "type": "text", "x": 600, "y": 50, "width": 220, "height": 120, "text": "# 优惠券可用性判断\n\n**规则验证：**\n- 立减券：直接可用\n- 满减券：订单金额≥门槛\n- 折扣券：商品金额≥门槛\n- 有效期检查", "color": "3"}, {"id": "discount-calculation", "type": "text", "x": 100, "y": 200, "width": 220, "height": 120, "text": "# 折扣金额计算\n\n**策略模式：**\n- FixedDiscountStrategy\n- ThresholdStrategy\n- DiscountStrategy\n- 工厂模式选择策略", "color": "4"}, {"id": "user-select-coupon", "type": "text", "x": 350, "y": 200, "width": 220, "height": 120, "text": "# 用户选择优惠券\n\n**前端交互：**\n- 展示可用优惠券列表\n- 显示折扣金额\n- 用户选择最优优惠券\n- 确认使用", "color": "5"}, {"id": "create-settlement-record", "type": "text", "x": 600, "y": 200, "width": 220, "height": 120, "text": "# 创建结算单\n\n**Engine模块：**\n- 分布式锁保护\n- 创建CouponSettlement\n- 状态：0(锁定)\n- 编程式事务控制", "color": "6"}, {"id": "lock-coupon-status", "type": "text", "x": 100, "y": 350, "width": 220, "height": 120, "text": "# 锁定优惠券状态\n\n**状态变更：**\n- UNUSED → LOCKING\n- 从Redis缓存中移除\n- 防止重复使用\n- 乐观锁更新", "color": "1"}, {"id": "order-payment", "type": "text", "x": 350, "y": 350, "width": 220, "height": 120, "text": "# 订单支付\n\n**支付流程：**\n- 计算最终应付金额\n- 调用支付接口\n- 支付成功回调\n- 触发优惠券核销", "color": "2"}, {"id": "coupon-writeoff", "type": "text", "x": 600, "y": 350, "width": 220, "height": 120, "text": "# 优惠券核销\n\n**processPayment：**\n- 结算单状态：0→2(已支付)\n- 优惠券状态：LOCKING→USED\n- 分布式锁保护\n- 事务一致性保证", "color": "3"}, {"id": "order-complete", "type": "text", "x": 100, "y": 500, "width": 220, "height": 120, "text": "# 订单完成\n\n**最终状态：**\n- 订单支付成功\n- 优惠券已使用\n- 结算单已支付\n- 用户获得商品", "color": "4"}, {"id": "refund-scenario", "type": "text", "x": 350, "y": 500, "width": 220, "height": 120, "text": "# 退款场景处理\n\n**processRefund：**\n- 结算单状态：2→3(已退款)\n- 优惠券状态：USED→UNUSED\n- 重新加入Redis缓存\n- 支持再次使用", "color": "5"}, {"id": "cancel-scenario", "type": "text", "x": 600, "y": 500, "width": 220, "height": 120, "text": "# 取消订单场景\n\n**订单取消：**\n- 结算单状态：0→1(已取消)\n- 优惠券状态：LOCKING→UNUSED\n- 释放锁定资源\n- 恢复可用状态", "color": "6"}, {"id": "exception-handling", "type": "text", "x": 100, "y": 650, "width": 220, "height": 120, "text": "# 异常处理机制\n\n**容错设计：**\n- 分布式锁超时释放\n- 事务回滚机制\n- 状态一致性检查\n- 重试补偿机制", "color": "1"}, {"id": "performance-optimization", "type": "text", "x": 350, "y": 650, "width": 220, "height": 120, "text": "# 性能优化\n\n**优化策略：**\n- Redis Pipeline批量操作\n- CompletableFuture异步处理\n- 编程式事务减小范围\n- 缓存预热和更新", "color": "2"}, {"id": "monitoring-logging", "type": "text", "x": 600, "y": 650, "width": 220, "height": 120, "text": "# 监控与日志\n\n**可观测性：**\n- 关键操作日志记录\n- 性能指标监控\n- 异常告警机制\n- 业务数据统计", "color": "3"}], "edges": [{"id": "edge-1", "fromNode": "user-order-start", "toNode": "query-available-coupons", "color": "1", "label": "1. 查询优惠券"}, {"id": "edge-2", "fromNode": "query-available-coupons", "toNode": "coupon-availability-check", "color": "2", "label": "2. 可用性判断"}, {"id": "edge-3", "fromNode": "coupon-availability-check", "toNode": "discount-calculation", "color": "3", "label": "3. 计算折扣"}, {"id": "edge-4", "fromNode": "discount-calculation", "toNode": "user-select-coupon", "color": "4", "label": "4. 用户选择"}, {"id": "edge-5", "fromNode": "user-select-coupon", "toNode": "create-settlement-record", "color": "5", "label": "5. 创建结算单"}, {"id": "edge-6", "fromNode": "create-settlement-record", "toNode": "lock-coupon-status", "color": "6", "label": "6. 锁定优惠券"}, {"id": "edge-7", "fromNode": "lock-coupon-status", "toNode": "order-payment", "color": "1", "label": "7. 订单支付"}, {"id": "edge-8", "fromNode": "order-payment", "toNode": "coupon-writeoff", "color": "2", "label": "8. 优惠券核销"}, {"id": "edge-9", "fromNode": "coupon-writeoff", "toNode": "order-complete", "color": "3", "label": "9. 订单完成"}, {"id": "edge-10", "fromNode": "coupon-writeoff", "toNode": "refund-scenario", "color": "4", "label": "退款分支"}, {"id": "edge-11", "fromNode": "lock-coupon-status", "toNode": "cancel-scenario", "color": "5", "label": "取消分支"}, {"id": "edge-12", "fromNode": "order-payment", "toNode": "exception-handling", "color": "6", "label": "异常处理"}, {"id": "edge-13", "fromNode": "query-available-coupons", "toNode": "performance-optimization", "color": "1", "label": "性能优化"}, {"id": "edge-14", "fromNode": "coupon-writeoff", "toNode": "monitoring-logging", "color": "2", "label": "监控日志"}]}